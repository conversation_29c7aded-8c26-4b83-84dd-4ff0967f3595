import { NextRouter } from 'next/router' // Assuming Next.js router
import { setMultiSelectedValueToWildCard } from '../arrayUtils'
import { Routes } from '@/src/constant/enum'
import { IStatus } from '@/src/redux/status/interface'

export const redirectToStatus = (
  phaseId: number[],
  stageStatusId: number,
  subStageId: number | null,
  statuses: IStatus[],
  router: NextRouter,
): void => {
  console.group('start-->')
  let redirectStatus: IStatus | undefined
  // If subStageId is provided, filter by subStageId as well
  const currentPhaseIDs = phaseId?.length > 0 ? setMultiSelectedValueToWildCard(phaseId) : ''
  const modifyRecord = statuses.map((item) => ({
    ...item,
    phaseIDs:
      item?.LookupProjectToPhase && item?.LookupProjectToPhase?.length > 0
        ? setMultiSelectedValueToWildCard(item.LookupProjectToPhase.map((phase) => phase.id))
        : '',
  }))

  console.log('**currentPhaseIDs: ', currentPhaseIDs)
  console.log(
    '**modifyRecord: ',
    modifyRecord.map((item) => item.phaseIDs),
  )
  // if (subStageId) {
  //   redirectStatus = statuses.find((item) => {
  //     return (
  //       (phaseId?.length === 0 || !phaseId
  //         ? item?.LookupProjectToPhase?.length === 0
  //           ? item?.LookupProjectToPhase?.length === 0
  //           : item?.LookupProjectToPhase?.some((lookup: any) => lookup?.phase === null)
  //         : !!item?.LookupProjectToPhase?.find((res) => phaseId.find((id) => id === res.id))) &&
  //       item?.MasterProjectStageStatus?.id === stageStatusId &&
  //       item?.MasterProjectSubStage?.id === subStageId
  //     )
  //   })
  // } else {
  //   console.log('stage', stageStatusId)
  //   console.log('phaseId', phaseId)
  //   console.log('haseId?.length === 0 || !phaseId: ', phaseId?.length === 0 || !phaseId)
  //   const test = [...statuses].sort((a, b) => {
  //     const aEmpty = !a.LookupProjectToPhase || a.LookupProjectToPhase.length === 0
  //     const bEmpty = !b.LookupProjectToPhase || b.LookupProjectToPhase.length === 0
  //     if (aEmpty === bEmpty) return 0
  //     return aEmpty ? -1 : 1
  //   })
  //   redirectStatus = test.find((item) => {
  //     return (
  //       (phaseId?.length === 0 || !phaseId
  //         ? item?.LookupProjectToPhase?.length === 0
  //           ? item?.LookupProjectToPhase?.length === 0
  //           : item?.LookupProjectToPhase?.some((lookup: any) => lookup?.phase === null)
  //         : !!item?.LookupProjectToPhase?.find((res) => phaseId?.find((id) => id === res.id))) &&
  //       item?.MasterProjectStageStatus?.id === stageStatusId
  //     )
  //   })
  // }

  // If subStage is provided, filter by subStage as well
  if (subStageId) {
    redirectStatus = modifyRecord.find(
      (item) =>
        (currentPhaseIDs === null || currentPhaseIDs === '' || !currentPhaseIDs
          ? item?.phaseIDs === '' || item?.phaseIDs === null
          : item.phaseIDs === currentPhaseIDs) &&
        item?.MasterProjectStageStatus?.id === stageStatusId &&
        item?.MasterProjectSubStage?.id === subStageId,
    )
  } else {
    console.log('here')
    redirectStatus = modifyRecord.find(
      (item) =>
        (currentPhaseIDs === null || currentPhaseIDs === '' || !currentPhaseIDs
          ? item?.phaseIDs === '' || item?.phaseIDs === null
          : item.phaseIDs === currentPhaseIDs) && item?.MasterProjectStageStatus?.id === stageStatusId,
    )
  }
  console.log('redirectStatus: ', redirectStatus)
  console.groupEnd()
  if (!redirectStatus) return

  const searchParam = router?.query?.search ? `?search=${router.query.search}` : ''
  router.push(`${Routes.EDIT_STATUS}/${redirectStatus.id}${searchParam}`)
}
