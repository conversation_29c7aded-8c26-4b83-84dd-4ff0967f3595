import React, { useEffect, useMemo, useState } from 'react'
import { FormikProps } from 'formik'
import Button from '../../shared/button'
import ComboBox from '../../shared/combobox'
import MultiAutoSelect from '../../shared/multiAutoSelect'
import TextInputField from '../../shared/textInputField'
import CancelRoundedIcon from '../../svgImages/cancelRoundedIcon'
import CorrectIcon from '../../svgImages/correctIcon'
import styles from '../AddProjectDrawer.module.scss'
import { useGetControlManagers } from '@/src/hooks/useControlManager'
import { useGetDirectors } from '@/src/hooks/useDirectors'
import { useGetEntityCategory } from '@/src/hooks/useEntityCategory'
import { useGetMasterExecutiveDirectors } from '@/src/hooks/useExecutiveDirector'
import { useGetLocations } from '@/src/hooks/useLocations'
import { useGetOwningEntity } from '@/src/hooks/useOwningEntity'
import { useGetPortfolioManagers } from '@/src/hooks/usePortfolioManager'
import { useGetPricingType } from '@/src/hooks/usePricingType'
// import { useGetProcurementManagers } from '@/src/hooks/useProcurementManagement'
import { useGetProjectClassifications } from '@/src/hooks/useProjectClassification'
import { useGetProjectOwners } from '@/src/hooks/useProjectOwner'
import { useGetSubLocations } from '@/src/hooks/useSubLocations'
import { useGetTypologies } from '@/src/hooks/useTypology'
import {
  convertMultiSelectOption,
  getValue,
  getValueForMultiSelectOption,
  populateDropdownOptions,
  prepareDropdownOptions,
} from '@/src/utils/arrayUtils'

interface IAddProjectForm {
  formik: FormikProps<any>
  editRow: any | null
  onDiscard: () => void
  isEnable: boolean
  isEditForUser?: boolean
}

const AddProjectForm = ({ formik, editRow, onDiscard, isEnable, isEditForUser = true }: IAddProjectForm) => {
  const [loader, setLoader] = useState<boolean>(isEnable)
  const { owningEntities } = useGetOwningEntity(isEnable)
  const { typologies } = useGetTypologies(isEnable)
  const { projectClassifications } = useGetProjectClassifications(isEnable)
  const { pricingTypes } = useGetPricingType(isEnable)
  const { entityCategories } = useGetEntityCategory(isEnable)
  const { locations } = useGetLocations(isEnable)
  const { subLocations } = useGetSubLocations(isEnable)
  const { portfolioManagers } = useGetPortfolioManagers(isEnable)
  // const { procureManagements } = useGetProcurementManagers(isEnable)
  const { controlManagers } = useGetControlManagers(isEnable)
  const { executiveDirectors } = useGetMasterExecutiveDirectors(isEnable)
  const { projectOwners } = useGetProjectOwners(isEnable)
  const { directors } = useGetDirectors(isEnable)

  // Fetch data only when addProject is true
  const fetchData = async () => {
    setLoader(true)
    try {
      await Promise.all([
        // getProjectOwnersApi(),
        // getProcurementManagementApi(),
        // getMasterOwningEntityApi(),
        // getMasterControlManagersApi(),
        // getMasterExecutiveDirectorsApi(),
        // getMasterTypologiesApi(),
        // getMasterPortfolioManagersApi(),
        // getPricingTypeApi(),
        // getEntityCategoryApi(),
        // getMasterLocationsApi(),
        // getMasterSubLocationsApi(),
        // getMasterProjectClassificationsApi(),
        // getSvpsApi(),
        // getDirectorsApi(),
      ])
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setLoader(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  const owningEntitiesOptions = useMemo(() => {
    if (loader) return [{ label: 'Loading...', value: '' }]
    return populateDropdownOptions(owningEntities, 'owning_entity')
  }, [owningEntities, loader])

  const project_typeOptions = useMemo(() => {
    if (loader) return [{ label: 'Loading...', value: '' }]
    return populateDropdownOptions(typologies, 'master_typology')
  }, [typologies, loader])

  const portfolio_managerOption = useMemo(() => {
    if (loader) return [{ label: 'Loading...', value: '' }]
    return prepareDropdownOptions(portfolioManagers, 'portfolio_manager', true)
  }, [portfolioManagers, loader])

  const controls_managersOptions = useMemo(() => {
    if (loader) return [{ label: 'Loading...', value: '' }]
    return prepareDropdownOptions(controlManagers, 'control_manager', true)
  }, [controlManagers, loader])

  const executive_directorsOptions = useMemo(() => {
    if (loader) return [{ id: '', name: 'Loading...', avatar: '' }] // Handle loader state
    // return executiveDirectors?.map((item: any) => ({
    //   id: item?.executive_director,
    //   name: item?.executive_director,
    //   avatar: item?.avatar?.url,
    // }))
    return prepareDropdownOptions(executiveDirectors, 'executive_director', true)
  }, [executiveDirectors, loader])

  const pricingTypeOptions = useMemo(() => {
    if (loader) return [{ label: 'Loading...', value: '' }]
    // return populateDropdownOptions(pricingTypes, 'pricing_type')
    return prepareDropdownOptions(pricingTypes, 'pricing_type')
  }, [pricingTypes, loader])

  const entityCategoriesOptions = useMemo(() => {
    if (loader) return [{ label: 'Loading...', value: '' }]
    // return populateDropdownOptions(entityCategories, 'entity_category')
    return prepareDropdownOptions(entityCategories, 'entity_category')
  }, [entityCategories, loader])

  const directorsOption = useMemo(() => {
    if (loader) return [{ label: 'Loading...', value: '' }]
    return prepareDropdownOptions(directors, 'director', true)
  }, [directors, loader])

  const designProjectOwnerOptions = useMemo(() => {
    if (loader) return [{ id: '', name: 'Loading...', avatar: '' }] // Handle loader state
    return prepareDropdownOptions(projectOwners, 'design_executive_director', true)
    // return (
    //   projectOwners?.map((item: any) => ({
    //     id: item?.design_executive_director || '', // Assuming this is the unique identifier
    //     name: item?.design_executive_director || 'Unknown', // Fallback if name is undefined
    //     avatar: item?.avatar?.url || '', // Optional avatar
    //   })) || []
    // )
  }, [projectOwners, loader])

  const subLocationOptions = useMemo(() => {
    if (loader) return [{ label: 'Loading...', value: '' }]
    return prepareDropdownOptions(subLocations, 'sub_location')
  }, [subLocations, loader])

  const classificationOptions = useMemo(() => {
    if (loader) return [{ label: 'Loading...', value: '' }]
    // return populateDropdownOptions(projectClassifications, 'project_classification')
    return prepareDropdownOptions(projectClassifications, 'project_classification')
  }, [projectClassifications, loader])

  // const procureManagementsOptions = useMemo(() => {
  //   if (loader) return [{ label: 'Loading...', value: '' }]
  //   return (
  //     procureManagements?.map((item: any) => ({
  //       label: item?.procurement_manager_name,
  //       avatar: item?.avatar?.url,
  //       value: item?.procurement_manager_name,
  //     })) || []
  //   )
  // }, [procureManagements, loader])

  const locationOptions = useMemo(() => {
    if (loader) return [{ label: 'Loading...', value: '' }]
    return prepareDropdownOptions(locations, 'location')
  }, [locations, loader])

  const handleExecutiveDirector = (selectedOptions: any[]) => {
    const ids = selectedOptions.map((item) => item).filter((id) => id !== null && id !== '')

    formik.setValues({
      ...formik.values,
      executive_director_ids: Array.from(new Set(ids)),
    })
  }

  const handleDesignProjectOwner = (selectedOptions: string[]) => {
    const ids = selectedOptions.map((item) => item).filter((id) => id !== null && id !== '')
    formik.setValues({
      ...formik.values,
      design_executive_director_ids: Array.from(new Set(ids)),
    })
  }

  const handleSubLocation = (selectedOptions: any[]) => {
    const ids = selectedOptions.map((item) => item).filter((id) => id !== null && id !== '')

    formik.setValues({
      ...formik.values,
      sub_location_ids: Array.from(new Set(ids)),
    })
  }

  const handleLocation = (selectedOptions: any[]) => {
    const ids = selectedOptions
      .map((item) => {
        if (typeof item === 'number') {
          return item
        }
        if (typeof item === 'object' && item !== null && 'value' in item) {
          return item.value
        }
        if (typeof item === 'string') {
          const found = locationOptions.find((opt: any) => opt.label === item)
          return found ? found.value : null
        }
        return null
      })
      .filter((id) => id !== null && id !== '')

    formik.setValues({
      ...formik.values,
      location_ids: Array.from(new Set(ids)),
    })
  }

  const handleDesignProjectManager = (selectedOptions: string[]) => {
    formik.setValues({
      ...formik.values,
      design_project_manager: selectedOptions.filter((item: any) => item !== '').join(',') || '',
    })
  }

  return (
    <>
      <form className={styles.form} onSubmit={formik.handleSubmit}>
        <div className={styles.gridContainer}>
          {/* Project Name */}
          <TextInputField
            className={`${styles.textField} ${styles.projectNameField}`}
            name="project_name"
            labelText="Project Name *"
            placeholder=""
            variant="outlined"
            value={formik.values.project_name}
            onChange={formik.handleChange}
          />
          {/* Owning Entity */}
          <ComboBox
            focusCustomClass={styles.focusClass}
            className={styles.selectField}
            options={owningEntitiesOptions}
            labelText="Owning Entity *"
            placeholder=""
            value={
              formik.values.owning_entity
                ? {
                    label: formik.values.owning_entity,
                    value: formik.values.owning_entity,
                  }
                : null
            }
            clearIcon={true}
            onChange={(val) =>
              formik.setValues({
                ...formik.values,
                owning_entity: val?.value || '',
              })
            }
          />
          {/* Project Type */}
          <ComboBox
            focusCustomClass={styles.focusClass}
            className={styles.selectField}
            options={project_typeOptions}
            labelText="Project Type *"
            placeholder=""
            value={
              formik.values.project_type
                ? {
                    label: formik.values.project_type,
                    value: formik.values.project_type,
                  }
                : null
            }
            clearIcon={true}
            onChange={(val) =>
              formik.setValues({
                ...formik.values,
                project_type: val?.value || '',
              })
            }
          />
          {/* Portfolio Manager */}
          <ComboBox
            focusCustomClass={styles.focusClass}
            className={styles.selectField}
            isAvatarOption={true}
            options={portfolio_managerOption}
            labelText="Portfolio Manager *"
            placeholder=""
            value={
              formik.values.master_portfolio_manager_id
                ? getValue(portfolio_managerOption, formik.values.master_portfolio_manager_id)
                : null
            }
            clearIcon={true}
            onChange={(val) =>
              formik.setValues({
                ...formik.values,
                master_portfolio_manager_id: val?.value || '',
              })
            }
          />
          {/* Design Manager */}
          <MultiAutoSelect
            isSubOption={false}
            isSx={false}
            isAvatarOption={true}
            labelText="Design Executive Director"
            placeholder={formik.values.design_executive_director_ids?.length ? '' : ''}
            options={convertMultiSelectOption(designProjectOwnerOptions, null, [], true).filter(
              (item: any) => item.id !== null && item.name !== null,
            )}
            value={getValueForMultiSelectOption(
              convertMultiSelectOption(designProjectOwnerOptions, null, [], true),
              formik.values.design_executive_director_ids,
            )}
            handleSelectedOption={handleDesignProjectOwner}
            className={styles.multiSelect}
          />
          {/* Control Manager */}
          <ComboBox
            focusCustomClass={styles.focusClass}
            className={styles.selectField}
            isAvatarOption={true}
            options={controls_managersOptions}
            labelText="Control Manager"
            placeholder=""
            value={
              formik.values.master_control_manager_id
                ? getValue(controls_managersOptions, formik.values.master_control_manager_id)
                : null
            }
            clearIcon={true}
            onChange={(val) =>
              formik.setValues({
                ...formik.values,
                master_control_manager_id: val?.value || '',
              })
            }
          />
          {/* Delivery Executive Director */}
          <MultiAutoSelect
            isSubOption={false}
            isSx={false}
            isAvatarOption={true}
            labelText="Delivery Executive Director"
            placeholder={formik.values.executive_director_ids?.length ? '' : ''}
            options={convertMultiSelectOption(executive_directorsOptions, null, [], true).filter(
              (item: any) => item.id !== null && item.name !== null,
            )}
            value={getValueForMultiSelectOption(
              convertMultiSelectOption(executive_directorsOptions, null, [], true),
              formik.values.executive_director_ids,
            )}
            handleSelectedOption={handleExecutiveDirector}
            className={styles.multiSelect}
          />
          {/* Delivery Director */}
          <ComboBox
            focusCustomClass={styles.focusClass}
            className={styles.selectField}
            options={directorsOption}
            isAvatarOption={true}
            labelText="Delivery Director"
            placeholder=""
            value={
              formik.values.master_director_id ? getValue(directorsOption, formik.values.master_director_id) : null
            }
            clearIcon={true}
            onChange={(val) =>
              formik.setValues({
                ...formik.values,
                master_director_id: val?.value || '',
              })
            }
          />
          {/* Delivery PM
    <ComboBox
      focusCustomClass={styles.focusClass}
      className={styles.selectField}
      options={svpsOption}
      labelText="Delivery PM"
      placeholder=""
      value={
        formik.values.delivery_project_manager
          ? {
              label: formik.values.delivery_project_manager,
              value: formik.values.delivery_project_manager,
            }
          : null
      }
      onChange={(val) =>
        formik.setValues({
          ...formik.values,
          delivery_project_manager: val?.value || '',
        })
      }
    /> */}
          {/* Procurement Manager */}
          {/* <ComboBox
            focusCustomClass={styles.focusClass}
            className={styles.selectField}
            options={procureManagementsOptions}
            labelText="Procurement Manager"
            isAvatarOption={true}
            placeholder=""
            value={
              formik.values.master_procurement_manager_id
                ? getValue(procureManagementsOptions, formik.values.master_procurement_manager_id)
                : null
            }
            onChange={(val) => {
              formik.setValues({
                ...formik.values,
                master_procurement_manager_id: val?.value || '',
              })
            }
          /> */}
          {/* Design Manager */}
          {/*  
          //TODO:  Worked on changes the order of Design Executive Director By Vatsal Sakariya.
         <MultiAutoSelect
            isSubOption={false}
            isSx={false}
            isAvatarOption={true}
            labelText="Design Executive Director"
            placeholder={formik.values.design_executive_director_ids?.length ? '' : ''}
            options={convertMultiSelectOption(designProjectOwnerOptions, null, [], true).filter(
              (item: any) => item.id !== null && item.name !== null,
            )}
            value={getValueForMultiSelectOption(
              convertMultiSelectOption(designProjectOwnerOptions, null, [], true),
              formik.values.design_executive_director_ids,
            )}
            handleSelectedOption={handleDesignProjectOwner}
            className={styles.multiSelect}
          /> */}
          {/* Category */}
          <ComboBox
            focusCustomClass={styles.focusClass}
            className={styles.selectField}
            options={entityCategoriesOptions}
            labelText="Category *"
            placeholder=""
            value={
              formik.values.master_entity_category_id
                ? getValue(entityCategoriesOptions, formik.values.master_entity_category_id)
                : null
            }
            clearIcon={true}
            onChange={(val) =>
              formik.setValues({
                ...formik.values,
                master_entity_category_id: val?.value || '',
              })
            }
          />
          {/* Pricing Type */}
          <ComboBox
            focusCustomClass={styles.focusClass}
            className={styles.selectField}
            options={pricingTypeOptions}
            labelText="Pricing Type *"
            placeholder=""
            value={
              formik.values.master_pricing_type_id
                ? getValue(pricingTypeOptions, formik.values.master_pricing_type_id)
                : null
            }
            clearIcon={true}
            onChange={(val) =>
              formik.setValues({
                ...formik.values,
                master_pricing_type_id: val?.value || '',
              })
            }
          />
          {/* Project Classification */}
          <ComboBox
            focusCustomClass={styles.focusClass}
            className={styles.selectField}
            options={classificationOptions}
            labelText="Project Classification *"
            placeholder=""
            value={
              formik.values.master_project_classification_id
                ? getValue(classificationOptions, formik.values.master_project_classification_id)
                : null
            }
            clearIcon={true}
            onChange={(val) =>
              formik.setValues({
                ...formik.values,
                master_project_classification_id: val?.value || '',
              })
            }
          />
          {/* Location */}
          <MultiAutoSelect
            isSubOption={false}
            isSx={false}
            labelText="Location"
            placeholder={formik.values.location_ids?.length ? '' : ''}
            options={convertMultiSelectOption(locationOptions).filter(
              (item: any) => item.id !== null && item.name !== null,
            )}
            value={getValueForMultiSelectOption(convertMultiSelectOption(locationOptions), formik.values.location_ids)}
            handleSelectedOption={handleLocation}
            className={styles.multiSelect}
          />
          {/* Sub Location */}
          <MultiAutoSelect
            isSubOption={false}
            isSx={false}
            labelText="Sub Location"
            placeholder={formik.values.sub_location_ids?.length ? '' : ''}
            options={convertMultiSelectOption(subLocationOptions).filter(
              (item: any) => item.id !== null && item.name !== null,
            )}
            value={getValueForMultiSelectOption(
              convertMultiSelectOption(subLocationOptions),
              formik.values.sub_location_ids,
            )}
            handleSelectedOption={handleSubLocation}
            className={styles.multiSelect}
          />
        </div>

        {/* Note */}
        <div className={styles.noteOfProject}>*Mandatory field</div>

        {/* Add or Edit Buttons */}
        {editRow === null ? (
          <Button
            type="submit"
            disabled={
              !formik.values.project_name ||
              !formik.values.owning_entity ||
              !formik.values.master_entity_category_id ||
              !formik.values.master_portfolio_manager_id ||
              !formik.values.master_project_classification_id ||
              !formik.values.master_pricing_type_id ||
              !formik.values.project_type
            }
            className={styles.addProjectButton}
            onClick={(e) => {
              e.preventDefault()
              formik.handleSubmit()
            }}
          >
            + Add
          </Button>
        ) : (
          <div className={styles.editActionButtons}>
            <Button
              type="submit"
              disabled={
                !formik.values.project_name ||
                !formik.values.owning_entity ||
                !formik.values.master_entity_category_id ||
                !formik.values.master_portfolio_manager_id ||
                !formik.values.master_project_classification_id ||
                !formik.values.master_pricing_type_id ||
                !formik.values.project_type
              }
              startIcon={<CorrectIcon />}
              className={styles.addProjectButton}
            >
              Update
            </Button>
            <Button
              type="submit"
              startIcon={<CancelRoundedIcon />}
              className={styles.addProjectButton}
              color="secondary"
              onClick={() => {
                onDiscard()
                formik.resetForm()
              }}
            >
              Discard
            </Button>
          </div>
        )}
      </form>
    </>
  )
}

export default AddProjectForm
