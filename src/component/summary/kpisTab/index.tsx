import React, { useEffect, useMemo, useState } from 'react'
import { DndContext, closestCenter, PointerSensor, useSensor, useSensors } from '@dnd-kit/core'
import {
  arrayMove,
  horizontalListSortingStrategy,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { WarningAmberOutlined } from '@mui/icons-material'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import DragIndicatorIcon from '@mui/icons-material/DragIndicator'
import GridOnOutlinedIcon from '@mui/icons-material/GridOnOutlined'
import ViewListOutlinedIcon from '@mui/icons-material/ViewListOutlined'
import { FormControlLabel, Radio, RadioGroup } from '@mui/material'
import Tooltip from '@mui/material/Tooltip'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/router'
import { toast } from 'sonner'
import KpiDrawer from './kpiDrawer'
import styles from './KpisTab.module.scss'
import ConfirmDeleteModal from '../../confirmDeleteModal'
import PulseButton from '../../shared/button/pulseButton'
import Checkbox from '../../shared/checkbox'
import Drawer from '../../shared/drawer'
import Loader from '../../shared/loader'
import TextInputField from '../../shared/textInputField'
import TypographyField from '../../shared/typography'
import CancelRoundedIcon from '../../svgImages/cancelRoundedIcon'
import DeleteIcon from '../../svgImages/deleteIcon'
import EditIcon from '../../svgImages/editIcon'
import SaveIcon from '../../svgImages/saveIcon'
import { PROJECT_QUERY_KEY, PROJECTS_QUERY_KEY } from '@/src/hooks/useProjects'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import useProjectKpi from '@/src/redux/projectKpi/useProjectKpi'
import { getMasterOneProject, getProjects } from '@/src/services/projects'
import { formatNumberWithCommas } from '@/src/utils/numberUtils'
import { errorToast, successToast } from '@/src/utils/toastUtils'
import { canEditUser } from '@/src/utils/userUtils'

export const SortableItem = ({ id, children, className }: any) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  return (
    <div ref={setNodeRef} className={className} style={style}>
      {children({ attributes, listeners })}
    </div>
  )
}

const KpisTab = ({ selectedTab }: any) => {
  const {
    getProjectKpisApi,
    projectKpis: dirtyProjectKpis,
    addProjectKpiApi,
    deleteProjectKpiApi,
    updateProjectKpiApi,
    sortProjectKpiApi,
  } = useProjectKpi()
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const router = useRouter()
  const { currentUser } = useAuthorization()
  const [tableData, setTableData] = useState<any[]>([])
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [loader, setLoader] = useState(true)
  const [drawer, setDrawer] = useState(false)
  const [editKpis, setEditKpis] = useState<any[]>([])
  const [inputValues, setInputValues] = useState<any>({})
  const [checkboxValues, setCheckboxValues] = useState<any>({})
  const [view, setView] = useState('Card View')
  const sensors = useSensors(useSensor(PointerSensor))

  const projectKpis = useMemo(() => {
    return (
      dirtyProjectKpis
        ?.filter((item) => !!item?.kpi)
        ?.map((item: any) => {
          if (item?.kpi_value === '') {
            return { ...item, kpi_value: '0' }
          }
          return item
        }) || []
    )
  }, [dirtyProjectKpis])

  const { data: projects } = useQuery({
    queryKey: [PROJECTS_QUERY_KEY],
    queryFn: () => getProjects({ period: currentPeriod }),
    select: (response) => response.data,
  })

  const getProjectPayload = {
    projectName: encodeURIComponent(router.query.slug?.toString() as string),
    period: currentPeriod,
  }
  // const { kpis } = useGetKpis() //* NO need to use master kpi data as now missing data coming from project-kpi record (Confirmed by backed tame)
  const { data: project } = useQuery({
    queryKey: [PROJECT_QUERY_KEY],
    queryFn: () => getMasterOneProject(getProjectPayload),
    select: (response) => response.data,
    enabled: false,
  })

  const fetchData = async () => {
    setLoader(true)
    try {
      await Promise.all([
        // getMasterKpisApi(),
        getProjectKpisApi({ period: currentPeriod, project_name: router?.query?.slug as string }),
      ])
    } catch (error) {
      console.error('Error occurred while fetching the KPI data:', error)
    } finally {
      setLoader(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [router.query.slug, selectedTab])

  //***************************--------- NO need to use master kpi data as now missing data coming from project-kpi record (Confirmed by backed tame) -------------
  useEffect(() => {
    // if (!projects?.length || !kpis?.length) {
    //   setTableData([])
    //   return
    // }
    // const projectSlug = router.query.slug
    // const foundProject = projects.find((item: any) => item.project_name === projectSlug)
    // const filteredKpiData = kpis.filter((item) => item.project_type === foundProject?.project_type)

    // Enrich KPI data with project KPI details
    // const generateUniqueId = (index: any) => `${Date.now() + index + 1}-${Math.random().toString(36).substr(2, 9)}`
    // const enrichedKpiData = filteredKpiData.map((kpi, index) => {
    //   const projectKpi = projectKpis.find((item) => item.kpi === kpi.kpi && item.project_name === projectSlug)

    //   return {
    //     ...kpi,
    //     ...projectKpi,
    //     editId: generateUniqueId(index),
    //     kpi_value: projectKpi?.kpi_value || null,
    //     id: projectKpi?.id,
    //     id_for_update_order: kpi?.id,
    //     is_executive_kpi: projectKpi?.is_executive_kpi === 'true' ? true : false,
    //   }
    // })

    // Identify and enrich remaining project KPIs not covered in enrichedKpiData

    // const remainingKpiData = projectKpis
    //   .filter((item) => item.project_name === projectSlug && !enrichedKpiData.some((i) => i.id === item.id))
    //   .map((item, index) => {
    //     // Generate unique editId and set default values
    //     const editId = generateUniqueId(index)
    //     const isExecutiveKpi = item?.is_executive_kpi === 'true' ? true : false

    //     return {
    //       ...item,
    //       is_executive_kpi: isExecutiveKpi,
    //       editId,
    //       kpi_value: item.kpi_value,
    //     }
    //   })

    // Combine enriched and remaining KPIs
    const combinedKpiData = [...projectKpis].map((item: any, index) => ({
      ...item,
      is_executive_kpi: Boolean(item?.is_executive_kpi),
      kpi_sorting_order: item?.kpi_sorting_order,
    }))

    setTableData(combinedKpiData)

    // Initialize input and checkbox values
    setInputValues(
      combinedKpiData.reduce((acc: any, kpi: any) => {
        acc[kpi.kpi] = formatNumberWithCommas(kpi.kpi_value)
        return acc
      }, {}),
    )

    setCheckboxValues(
      combinedKpiData.reduce((acc: any, kpi: any) => {
        acc[kpi.kpi] = Boolean(kpi.is_executive_kpi)
        return acc
      }, {}),
    )
  }, [projects, projectKpis, router.query.slug])

  const handleCheckboxChange = async (e: React.ChangeEvent<HTMLInputElement>, kpi: any) => {
    const updatedCheckboxValue = e.target.checked
    setCheckboxValues((prev: any) => ({
      ...prev,
      [kpi.kpi]: updatedCheckboxValue,
    }))

    // const res: any = await updateProjectKpiApi({
    //   id: kpi.id,
    //   data: {
    //     period: currentPeriod,
    //     is_executive_kpi: updatedCheckboxValue ? 'true' : 'false',
    //     project_name: kpi.project_name,
    //     project_type: kpi.project_type,
    //     kpi: kpi.kpi,
    //     kpi_value: kpi.kpi_value,
    //     last_updated: new Date().toLocaleTimeString(),
    //   },
    // })

    // if (!res.payload.success) return
    // await getProjectKpisApi({ period: currentPeriod })
  }

  const handleInputChange = (kpi: string, value: string) => {
    const rawValue = value.replace(/,/g, '').replace(/\D/g, '')
    setInputValues((prevValues: any) => ({
      ...prevValues,
      [kpi]: rawValue,
    }))
  }

  const handleEdit = (kpi: any) => {
    setEditKpis((prev) => [...prev, kpi.id])
    // setEditKpis((prev) => [...prev, kpi.editId])
  }

  const handleDiscard = (kpi: any) => {
    setEditKpis((prev) => prev.filter((id) => id !== kpi.id))
    setInputValues((prevValues: any) => ({
      ...prevValues,
      [kpi.kpi]: formatNumberWithCommas(kpi.kpi_value || 0),
    }))
    // setEditKpis((prev) => prev.filter((id) => id !== kpi.editId))
  }

  const handleUpdate = async (kpi: any) => {
    const updatedKpiValue = inputValues[kpi.kpi]?.toString()?.includes(',')
      ? inputValues[kpi.kpi].replace(/,/g, '')
      : inputValues[kpi.kpi]
    const res: any = kpi.id
      ? await updateProjectKpiApi({
          id: kpi.id,
          data: {
            period: currentPeriod,
            project_name: project?.project_name,
            project_type: project?.project_type,
            kpi: kpi.kpi,
            kpi_value: parseFloat(String(updatedKpiValue)),
            is_executive_kpi: Boolean(checkboxValues[kpi.kpi]),
            last_updated: new Date(),
          },
        })
      : await addProjectKpiApi({
          period: currentPeriod,
          project_name: project?.project_name,
          project_type: project?.project_type,
          kpi: kpi.kpi,
          kpi_value: parseFloat(String(updatedKpiValue)),
          is_executive_kpi: Boolean(checkboxValues[kpi.kpi]),
          last_updated: new Date(),
        })

    if (!res.payload.success) {
      errorToast(res?.payload?.response?.data?.message || 'Failed')
      return
    }
    if (res.payload.success) {
      successToast(res?.payload?.message || 'KPI Updated Successfully')
    }

    await getProjectKpisApi({ period: currentPeriod, project_name: router?.query?.slug as string })
    setEditKpis([])
  }

  const handleDelete = async (kpi: any) => {
    const res: any = await deleteProjectKpiApi(kpi)
    if (!res.payload.success) {
      errorToast(res?.payload?.response?.data?.message || 'Failed')
      return
    } else {
      successToast(res?.payload?.message || 'KPI Updated Successfully')
    }
    await getProjectKpisApi({ period: currentPeriod, project_name: router?.query?.slug as string })
    setDeleteModel(null)
  }

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  const handleDragEnd = async (event: any) => {
    const { active, over } = event
    const cloneTableData: any[] = JSON.parse(JSON.stringify([...tableData]))
    if (active.id !== over?.id) {
      const oldIndex = cloneTableData.findIndex((item) => item.kpi === active.id)
      const newIndex = cloneTableData.findIndex((item) => item.kpi === over?.id)

      const newItems = arrayMove(cloneTableData, oldIndex, newIndex)

      // Update sorting order
      const updatedItems = newItems.map((item, index) => ({
        ...item,
        kpi_sorting_order: index + 1,
      }))

      setTableData(updatedItems)
      const newPayload = updatedItems?.map((item: any, index: number) => ({
        id: item?.id,
        kpi_sorting_order: item?.kpi_sorting_order,
      }))

      await sortProjectKpiApi({ period: currentPeriod, KPIRecords: newPayload })
    }
  }

  return (
    <>
      <div className={styles.cards}>
        <PulseButton
          onClick={() => {
            if (!isEditForUser) {
              toast(`The current reporting period is locked`, {
                icon: <WarningAmberOutlined />,
              })
            } else {
              setDrawer(true)
            }
          }}
          label="Add Item"
          icon={<AddOutlinedIcon fontSize="large" />}
          disabled={loader}
        />
        {/* <RadioGroup aria-label="menu-items" name="menu-items" value={view} className={styles.radioGroup}>
          {['List View', 'Card View'].map((item, index) => (
            <div key={index} onClick={() => setView(item)}>
              <FormControlLabel
                value={item}
                control={<Radio color="primary" />}
                label={item}
                style={{ margin: 0 }}
                checked={view === item}
              />
            </div>
          ))}
        </RadioGroup> */}
        <div className={styles.iconButton}>
          {['List View', 'Card View'].map((item, index) => (
            <div key={index} onClick={() => setView(item)} style={{ display: 'inline-block' }}>
              <Tooltip title={item} arrow>
                <FormControlLabel
                  value={item}
                  control={<div />}
                  label={
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      {item === 'List View' ? (
                        <ViewListOutlinedIcon
                          style={{ width: 27, height: 27, color: view === 'List View' ? '#4a4a4a' : '#b0b0b0' }}
                        />
                      ) : (
                        <GridOnOutlinedIcon
                          style={{ width: 20, height: 20, color: view === 'Card View' ? '#4a4a4a' : '#b0b0b0' }}
                        />
                      )}
                    </div>
                  }
                  style={{ margin: 0 }}
                  checked={view === item}
                />
              </Tooltip>
            </div>
          ))}
        </div>

        <Drawer anchor="right" open={drawer} onClose={() => setDrawer(false)}>
          <KpiDrawer
            drawerStates={drawer}
            onClose={() => setDrawer(false)}
            filterProjectKpiData={tableData}
            project={project}
            editId={null}
            setEditId={() => {}}
          />
        </Drawer>
        {loader ? (
          <Loader />
        ) : (
          <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
            <SortableContext
              items={tableData.map((item) => item.kpi)}
              strategy={view === 'Card View' ? horizontalListSortingStrategy : verticalListSortingStrategy}
            >
              <div className={view === 'Card View' ? styles.kpiCards : styles.listView}>
                {tableData
                  .sort((a, b) => a?.kpi_sorting_order - b?.kpi_sorting_order)
                  .map((kpi, index) => {
                    const showInput = view === 'Card View' ? true : inputValues[kpi.kpi] || editKpis.includes(kpi.id)
                    return (
                      <SortableItem
                        key={index}
                        id={kpi.kpi}
                        className={`${view === 'Card View' ? styles.card : styles.listItem} ${editKpis.includes(kpi.id) ? styles.highlightedField : ''}`}
                      >
                        {({ attributes, listeners }: any) => (
                          <div>
                            <span
                              className={styles.dragButton}
                              {...attributes}
                              style={{
                                top: view === 'Card View' ? '18px' : '50%',
                                transform: view === 'Card View' ? '' : 'translateY(-40%)',
                              }}
                              {...listeners}
                            >
                              <DragIndicatorIcon />
                            </span>
                            <div className={styles.header}>
                              <div className={view === 'Card View' ? styles.cardDetails : styles.listItemDetails}>
                                <TypographyField
                                  variant={'body1'}
                                  text={kpi.kpi}
                                  className={styles.headerDetails}
                                  style={{
                                    width: view === 'Card View' ? '200px' : `${kpi.kpi.length + 1}ch`,
                                    whiteSpace: view === 'List View' ? 'nowrap' : 'wrap',
                                  }}
                                />
                                {showInput && (
                                  <TextInputField
                                    variant="outlined"
                                    className={`${styles.textField} ${editKpis.includes(kpi.id) ? styles.highlightedTextField : ''} `}
                                    disabled={!editKpis.includes(kpi.id)}
                                    onChange={(e) => handleInputChange(kpi.kpi, e.target.value)}
                                    value={
                                      inputValues[kpi.kpi]
                                        ? formatNumberWithCommas(inputValues[kpi.kpi])
                                        : inputValues[kpi.kpi] === 0
                                          ? '0'
                                          : ''
                                    }
                                  />
                                )}
                              </div>
                              <div
                                className={styles.actions}
                                style={{ flexDirection: view === 'Card View' ? 'column' : 'row' }}
                              >
                                {Number(project?.is_executive_project) ? (
                                  <Checkbox
                                    className={`${view === 'List View' ? styles.checkBox : styles.gridView}`}
                                    disabled={!editKpis.includes(kpi.id)}
                                    name="is_executive_project"
                                    checked={Boolean(checkboxValues[kpi.kpi]) || false}
                                    onChange={(e) => {
                                      if (!isEditForUser) {
                                        toast(`The current reporting period is locked`, {
                                          icon: <WarningAmberOutlined />,
                                        })
                                      } else {
                                        handleCheckboxChange(e, kpi)
                                      }
                                    }}
                                  />
                                ) : null}
                                <div className={styles.actionOfKpi}>
                                  <div className={styles.action}>
                                    {kpi?.id && view === 'List View' && (
                                      <div className={styles.deleteContainer}>
                                        <DeleteIcon
                                          onClick={() => {
                                            if (!isEditForUser) {
                                              toast(`The current reporting period is locked`, {
                                                icon: <WarningAmberOutlined />,
                                              })
                                            } else {
                                              setDeleteModel(kpi.id)
                                            }
                                          }}
                                        />
                                      </div>
                                    )}
                                    {/* {!editKpis.includes(kpi.editId) ? ( */}
                                    {!editKpis.includes(kpi.id) ? (
                                      <EditIcon
                                        onClick={() => {
                                          if (!isEditForUser) {
                                            toast(`The current reporting period is locked`, {
                                              icon: <WarningAmberOutlined />,
                                            })
                                          } else {
                                            handleEdit(kpi)
                                          }
                                        }}
                                      />
                                    ) : (
                                      <>
                                        <CancelRoundedIcon onClick={() => handleDiscard(kpi)} />
                                        <SaveIcon color="#000000" onClick={() => handleUpdate(kpi)} />
                                      </>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>

                            {kpi?.id && view === 'Card View' && (
                              <div className={styles.deleteContainer}>
                                <DeleteIcon
                                  onClick={() => {
                                    if (!isEditForUser) {
                                      toast(`The current reporting period is locked`, {
                                        icon: <WarningAmberOutlined />,
                                      })
                                    } else {
                                      setDeleteModel(kpi.id)
                                    }
                                  }}
                                />
                              </div>
                            )}
                          </div>
                        )}
                      </SortableItem>
                    )
                  })}
              </div>
            </SortableContext>
          </DndContext>
        )}
      </div>
      {deleteModel && (
        <ConfirmDeleteModal
          open={deleteModel ? true : false}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDelete(deleteModel as number)}
        />
      )}
    </>
  )
}

export default KpisTab
