@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.cards {
  display: flex;
  // flex-direction: column;
  flex-wrap: wrap;
  gap: 12px;
  // height: 100%;
  padding: 15px 0px 15px 0px;
  .card {
    box-shadow: 0 10px 10px rgba(0, 6, 4, 0.25);
    border-radius: 4px;
    background: $WHITE;
    padding: 20px 20px 20px 30px;
    max-width: 230px;
    width: 100%;
    position: relative;
  }
}

.textField {
  > div {
    height: 30px !important;
  }
  > div {
    width: 100%;
    > div > input {
      width: 100% !important;
      text-overflow: unset !important;
      background: white !important;
      padding: 0px !important;
      color: $DARK;
      font-size: 24px !important;
      font-weight: 600 !important;
      line-height: 30px !important;
    }
  }
}

.actionButtons {
  cursor: pointer;
}
.kpiCards {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  @include respond-to('mobile') {
    justify-content: center;
  }
  @include respond-to('tablet') {
    padding-left: 80px;
    justify-content: unset;
  }
  @include respond-to('laptop') {
    padding-left: 20px;
  }
}
.header {
  display: flex;
  justify-content: space-between;
  .headerDetails {
    font-size: 12px !important;
  }
  svg {
    cursor: pointer;
  }
}
.action {
  display: flex;
  align-items: center;
  gap: 4px;
}
.addButton {
  background-color: #495e69 !important;
  border-radius: 4px 18px 18px 4px !important;
  gap: 6px;
  padding: 0 0 0 9px !important;
  color: #fff !important;
}
.deleteContainer {
  display: flex;
  // flex-direction:;
  justify-content: flex-end;
  margin-right: 4px;
  svg {
    cursor: pointer;
  }
}
.actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  // gap: 0.5rem;
}
.checkBox {
  padding: 0px !important;
  margin-right: 10px !important;
  // margin-top: 10px;
}
// .actionOfKpi {
//   // margin-top: 10px;
// }

.radioGroup {
  display: flex !important;
  flex-direction: row !important;
  gap: 10px !important;
  align-items: center !important;
}

.iconButton {
  margin-left: auto !important;
  display: flex !important;
  gap: 10px !important;
  margin-right: 50px !important;
}

.listView {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0px 0px 0px 0px;
  gap: 10px;

  .listItem {
    box-shadow: 0 10px 10px rgba(0, 6, 4, 0.25);
    border-radius: 4px;
    background: $WHITE;
    padding: 20px 20px 20px 30px;
    margin-right: 1.25rem;
    position: relative;
    align-items: center;
  }
}

.dragButton {
  position: absolute;
  left: 5px;
  cursor: grab;
}

.cardDetails {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.listItemDetails {
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: center;
}

.highlightedField {
  border-radius: 5px;
  background: #f0f8ff !important;
  border: 1px solid rgb(40 101 220) !important;
}

.highlightedTextField {
  > div > div > input {
    background-color: #f0f8ff !important;
  }
}
