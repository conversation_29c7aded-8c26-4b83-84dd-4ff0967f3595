import React, { useEffect, useMemo, useRef, useState } from 'react'
import { RestartAlt, WarningAmberOutlined } from '@mui/icons-material'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import EditNoteIcon from '@mui/icons-material/EditNote'
import { Box, Chip, Divider, Icon, Paper, Popover, Stack, Tooltip, Typography } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'
import { toast } from 'sonner'
import { IRowIdDataById, IStatusForTable } from './interface'
import LeadTimeModel from './leadTimeModel'
import { runValidation } from './phaseAndStageTab/helper'
import ValidationError from './phaseAndStageTab/validationError'
import {
  onCellService,
  handleLeadTime,
  getAdditionalUpdateData,
  slippageJustificationValidation,
} from './statusService'
import styles from './StatusTab.module.scss'
import { shouldHighlightForecastDate, shouldSuccessorHaveDifferentVariance } from './util'
import { validateStatus } from '../../../utils/progressForm/statusValidation'
import ConfirmationModal from '../../confirmationModal'
import DatePickerWithCheckbox from '../../customCells/datePickerWithCheckbox'
import ProgressCell from '../../customCells/progressCell'
import ReplaceMultiPhaseModal from '../../ReplaceMultiPhaseModal'
import RichTextEditor from '../../richTextEditor'
import Button from '../../shared/button'
import PulseButton from '../../shared/button/pulseButton'
import Checkbox from '../../shared/checkbox'
import Drawer from '../../shared/drawer'
import Loader from '../../shared/loader'
import CommonPopoverForDisplayMultiSelect from '../../shared/popover/commonPopoverForDisplayMultiSelect'
import PulseModel from '../../shared/pulseModel'
import TanStackTable from '../../shared/tanStackTable'
import { dateSortingFn, multiValueSorting, numericValueSorting } from '../../shared/tanStackTable/helper'
import { CustomColumnDef } from '../../shared/tanStackTable/interface'
import EditIcon from '../../svgImages/editIcon'
import ValidationModel from '../../updateProgress/progressForm/validationModel'
import { WHITE } from '@/src/constant/color'
import { multiSelectOption, Routes } from '@/src/constant/enum'
import { MULTI_SELECT_SEPARATOR } from '@/src/constant/stageStatus'
import { useCascadeForeCast } from '@/src/hooks/useCascadeForeCast'
import { PROJECT_QUERY_KEY, useUpdateProject } from '@/src/hooks/useProjects'
import useAreaOfConcern from '@/src/redux/areaOfConcern/useAreaOfConcern'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
// import useCategory from '@/src/redux/category/useCategory'
import useCommercial from '@/src/redux/commercial/useCommercial'
import useKeyAchievement from '@/src/redux/keyAchievement/useKeyAchievement'
import useLastUpdated from '@/src/redux/lastUpdated/useLastUpdated'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import usePhase from '@/src/redux/phase/usePhase'
import useProjectManagement from '@/src/redux/projectManagement/useProjectManagement'
import { IStatus } from '@/src/redux/status/interface'
import useStatus from '@/src/redux/status/useStatus'
import useFile from '@/src/redux/uploadPicture/useFile'
import { getMasterOneProject } from '@/src/services/projects'
import {
  convertAndSortData,
  convertMultiSelectOption,
  prepareDropdownOptions,
  prepareMultiPhaseCategoryDropdownOptions,
  sortArrayByKeyWithTypeConversion,
} from '@/src/utils/arrayUtils'
import { DragAndDropResponse, dragNDropStatus } from '@/src/utils/statusTab/dragNDropStatus'
import { isRecentStatusUpdate } from '@/src/utils/statusTab/recentUpdates'
import { getStageStatusByPermission } from '@/src/utils/statusTab/stageStatusByPermission'
import { generateStatusTableData } from '@/src/utils/statusTab/statusForTable'
import { errorToast, successToast } from '@/src/utils/toastUtils'
import {
  canEditUser,
  hasDesignTeamRole,
  hasUpdateBaselinePlanFinishDateAndPlanDurationPermission,
} from '@/src/utils/userUtils'

export const getUniqueValues = (array: any[], key: string) => [...new Set(array?.map((item) => item[key]))]

const PhaseAndStageTab = dynamic(() => import('./phaseAndStageTab'), {
  loading: () => <Loader />,
  ssr: false,
})

const StatusTab = ({
  selectedTab,
  expanded,
  gridFilters,
  setGridFilters,
}: {
  selectedTab: string
  expanded: boolean
  gridFilters: { colId: string; values: any }[]
  setGridFilters: (args: { colId: string; values: any }[]) => void
}) => {
  // State Hooks
  const [isStageDrawer, setIsStageDrawer] = useState(false)
  const [validationModel, setValidationModel] = useState<any>(null)
  const [openValidationModel, setOpenValidationModel] = useState(false)
  const [selectRecords, setSelectRecords] = useState<number[]>([])
  const [isEditTable, setIsEditTable] = useState(false)
  const [statusData, setStatusData] = useState<IStatusForTable[]>([])
  const [loader, setLoader] = useState(true)
  const { updateOverallPhaseWithMultiPhaseApi } = useFile()
  const [filters, setFilters] = useState<any>({
    phase: [],
    stageStatus: [],
    subStageStatus: [],
    contractor: [],
    pmcConsultant: [],
  })
  const [isValidationModel, setIsValidationModel] = useState(false)
  const [validationMessage, setValidationMessage] = useState<string[]>([])
  const [isLeadModel, setIsLeadModel] = useState<string | null>(null)
  const [isCascadedModal, setsCascadedModal] = useState<boolean>(false)
  const [isReplaceMultiPhaseModal, setReplaceMultiPhaseModal] = useState<boolean>(false)
  const [isForecastUpdating, setIsForecastUpdating] = useState<boolean>(false)
  const [rowIdDataById, setRowDataById] = useState<Record<string, IRowIdDataById> | null>(null) //Object of required data key name statusData
  const [isStageVisited, setIsStageVisited] = useState(false)
  const [isSubmittingPhase, setIsSubmittingPhase] = useState(false)
  const [selectedPhase, setSelectedPhase] = useState<{
    label: string
    value: string
    id: number
  } | null>(null)

  // Custom Hooks
  const {
    statuses,
    getStatusApi,
    updateStatusApi,
    setProgressTableColumnVisibilitiesApi,
    progressTableVisibilities,
    // progressFilterValue,
    // setProgressFilterValueApi,
  } = useStatus()
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const { setLocalPhaseApi, getMasterPhaseCategoryApi } = usePhase()
  // const { setLocalCategoryApi } = useCategory()
  const { getCommercialApi } = useCommercial()
  const { currentUser } = useAuthorization()
  // const { projectManagements, getProjectManagementsApi } = useProjectManagement()
  const { keyAchievements, getKeyAchievementsApi } = useKeyAchievement()
  const { areaOfConcerns, getAreaOfConcernsApi } = useAreaOfConcern()
  const { addLastUpdateOfProgressReducer, lastUpdateOfProgress } = useLastUpdated()
  const slippageJustification = useRef<string | null>(null)

  // anchor element for popover
  // Popover state
  const [categoryAnchorEl, setCategoryAnchorEl] = useState(null)
  // Other Hooks
  const router = useRouter()
  const popoverAnchorEl = useRef<HTMLElement | null>(null)

  const getProjectPayload = {
    projectName: encodeURIComponent(router.query.slug?.toString() as string),
    period: currentPeriod,
  }
  const { data: project, refetch } = useQuery({
    queryKey: [PROJECT_QUERY_KEY],
    queryFn: () => getMasterOneProject(getProjectPayload),
    select: (response) => response.data,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  })

  const { mutate: updateProject } = useUpdateProject()
  const { mutateAsync: updateForecastFinish } = useCascadeForeCast()
  const isMultiCategoryPopoverOpen = Boolean(categoryAnchorEl)

  // Fetch Status Data
  const getStatusData = async () => {
    const updatedData = generateStatusTableData(
      statuses,
      currentUser,
      router.query.slug as string,
      lastUpdateOfProgress,
    )

    const uniquePhases = getUniqueValues(updatedData, 'phase')
    const uniqueSubStageStatus = getUniqueValues(updatedData, 'subStatus')
    const uniqueStageStatuses = getUniqueValues(updatedData, 'stageStatus')
    const uniqueContractors = getUniqueValues(updatedData, 'contractor')
    const uniquePmcConsultants = getUniqueValues(updatedData, 'pmcConsultant')

    // Set filters
    setFilters({
      phase: [multiSelectOption?.SELECT_ALL, ...uniquePhases],
      subStageStatus: [...uniqueSubStageStatus],
      stageStatus: [multiSelectOption.SELECT_ALL, ...uniqueStageStatuses],
      contractor: [...uniqueContractors, multiSelectOption.SELECT_ALL],
      pmcConsultant: [...uniquePmcConsultants, multiSelectOption.SELECT_ALL],
    })
    setStatusData(updatedData)

    //* Create object key name same as row id and value as per requirement
    const rowDataById: Record<string, IRowIdDataById> = updatedData.reduce(
      (acc, item) => {
        if (item.id !== undefined && item.id !== null) {
          acc[item.id.toString()] = { isBaselinePlanFinishEdit: false }
        }
        return acc
      },
      {} as Record<string, IRowIdDataById>,
    )

    setRowDataById(rowDataById)
  }

  // Update a specific value for a given id in rowIdDataById
  const updateRowIdDataById = (id: string, newValue: Partial<IRowIdDataById>) => {
    setRowDataById((prev) => {
      if (!prev) return prev
      return {
        ...prev,
        [id]: { ...prev[id], ...newValue },
      }
    })
  }

  // Get the value for a given id from rowIdDataById
  const getRowIdDataById = (id: string) => {
    return rowIdDataById?.[id]
  }

  // Fetch Data
  const fetchData = async () => {
    try {
      await Promise.all([
        getStatusApi({ period: currentPeriod, project_name: router.query.slug?.toString() }),
        getCommercialApi({ period: currentPeriod, project_name: router.query.slug?.toString() }),
        // getProjectManagementsApi({ period: currentPeriod, project_name: router.query.slug?.toString() }),
        getKeyAchievementsApi({ period: currentPeriod, project_name: router.query.slug?.toString() }),
        getAreaOfConcernsApi({ period: currentPeriod, project_name: router.query.slug?.toString() }),
      ])
      setLoader(false)
    } catch (error) {
      console.error('Error fetching status data:', error)
    }
  }

  useEffect(() => {
    getMasterPhaseCategoryApi({ period: currentPeriod, project_name: project?.project_name })
  }, [])

  useEffect(() => {
    if (selectedTab === 'Progress') {
      // Call fetchData() when the drawer is closed
      if (!isStageDrawer) {
        setLoader(true)
        fetchData()
      }
    }
  }, [selectedTab, isStageDrawer])

  useEffect(() => {
    getStatusData()
  }, [statuses, lastUpdateOfProgress, isStageDrawer])

  // Handle Cell Update
  const onCellUpdate = async (cell: any, newValue: any, updateStatus: any) => {
    const status: any = statuses.find((item: any) => item.id.toString() === updateStatus?.id.toString())
    let updatedData: any = {
      [cell.columnId]: newValue, // Dynamically set the updated field
    }
    if (
      slippageJustification.current &&
      slippageJustificationValidation(
        status?.MasterProjectStageStatus?.project_stage_status,
        newValue,
        status?.forecast_completion_last_week,
      )
    ) {
      updatedData.slippage_justification = slippageJustification?.current
    }

    const getCommonUpdateData = (row: any) => {
      const payload: any = {}
      if (row.sortingOrder) {
        payload.project_status_sorting_order = row.sortingOrder
      }
      if (row.actualLWeek && row.actualLWeek !== '-') {
        payload.actual_progress_percentage_of_last_week = row.actualLWeek
      }
      if (row.contractor && row.contractor !== '-') {
        payload.contractor = row.contractor
      }
      if (row.pmcConsultant && row.pmcConsultant !== '-') {
        payload.pmc_consultant = row.pmcConsultant
      }
      if (row.planLWeek && row.planLWeek !== '-') {
        payload.actual_progress_percentage_for_last_week = row.planLWeek
      }
      if (row.phase) {
        payload.phase = row.phase
      }
      if (row.stageStatus) {
        // TODO : Need to check
        payload.stage_status = row.stageStatus
      }
      if (row.subStatus) {
        // TODO : Need to check
        payload.sub_stage = row.subStatus
      }
      // Always update the last_updated field
      payload.last_updated = new Date().toISOString()

      if (row.designStageWeightage && !isNaN(row.designStageWeightage)) {
        payload.design_stage_weightage = Number(row.designStageWeightage) / 100
      }
      if (row.phaseWeightage && !isNaN(row.phaseWeightage)) {
        payload.phase_weightage = (Number(row.phaseWeightage) / 100).toString()
      }

      payload.slippage_justification = slippageJustification?.current

      return payload
    }

    const additionalUpdateData = getAdditionalUpdateData(
      status?.MasterProjectStageStatus.project_stage_status,
      updatedData,
      true,
    )

    const values = { ...getCommonUpdateData(updatedData), ...additionalUpdateData }

    const allowedStatuses = statuses.filter((item: any) =>
      getStageStatusByPermission(currentUser.role).includes(item?.MasterProjectStageStatus?.project_stage_status),
    )
    const sortedStatuses = sortArrayByKeyWithTypeConversion(allowedStatuses, 'project_status_sorting_order', true)

    const validationMessages = await validateStatus(values, status as IStatus, sortedStatuses, 'StatusTable')
    if (validationMessages.length > 0) {
      setValidationMessage(validationMessages)
      setIsValidationModel(true)
    }
    if (validationMessages.length) return true

    return onCellService(
      cell,
      newValue,
      updateStatus,
      statuses,
      statusData,
      updateStatusApi,
      getStatusApi,
      currentPeriod,
      project,
      updateProject,
      refetch,
      slippageJustification?.current,
    )
  }

  // Handle Drag and Drop
  const handleDragAndDrop = async (data: any, dragId: string, dropId: string) => {
    if (!dragId || !dropId) {
      console.error('Invalid drag or drop ID.')
      return
    }

    const result: DragAndDropResponse | null = await dragNDropStatus(statuses, data, dragId, dropId)
    if (!result) {
      return null
    }

    const { updatedDropItem, dragItem, updatedDragItem, dropItem } = result

    const resDrag: Record<string, any> = await updateStatusApi({
      id: Number(dragId),
      data: { ...updatedDropItem, project_status_sorting_order: dragItem.project_status_sorting_order },
    })

    const resDrop: Record<string, any> = await updateStatusApi({
      id: Number(dropId),
      data: { ...updatedDragItem, project_status_sorting_order: dropItem.project_status_sorting_order },
    })

    if (resDrag.payload.success && resDrop.payload.success) {
      await getStatusApi({
        period: currentPeriod,
        project_name: project?.project_name,
      })
    }
  }

  // Memoized Edit Permissions
  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  useEffect(() => {
    const lastUpdateOfProgressResult = isRecentStatusUpdate(router.query.slug as string, statuses)
    addLastUpdateOfProgressReducer(lastUpdateOfProgressResult)
  }, [statuses, keyAchievements, areaOfConcerns])

  const handleClickCategoryPopover = (event: any) => {
    event.stopPropagation() // Prevent event bubbling
    setCategoryAnchorEl(event.currentTarget)
  }

  const handleCloseCategoryPopover = () => {
    setCategoryAnchorEl(null)
  }

  const checkAllPhaseNullOrEmpty = (array: any) => {
    if (array.length === 0) return true
    // Check if every object in the array has a phase value of null
    return array.every((obj: any) => obj.phase === null)
  }

  // Columns Definition
  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'isRibbon',
      header: 'Last Updated',
      size: 20,
      filterType: 'list',
      listOption: [
        { id: 'More Than 7 Days', name: 'More Than 7 Days' },
        { id: 'Not More Than 7 Days', name: 'Not More Than 7 Days' },
      ],
    },
    {
      accessorKey: 'checkBoxCol',
      header: '',
      visible: currentUser.role?.view_permissions.includes('Edit Lead Functionality'),
      cell: ({ row }) => {
        return (
          <div>
            <Checkbox
              checked={selectRecords?.includes(row?.original?.id)}
              onChange={() => {
                if (selectRecords?.includes(row?.original?.id)) {
                  const filterData = selectRecords?.filter((rec) => {
                    return rec !== row?.original?.id
                  })
                  setSelectRecords(filterData)
                } else {
                  setSelectRecords([...selectRecords, row?.original?.id])
                }
              }}
            />
          </div>
        )
      },
      size: 60,
    },
    // {
    //   accessorKey: 'dragCol',
    //   header: '',
    //   cell: ({ row }) => <DragCell rowId={row?.id} />,
    //   size: 40,
    //   align: 'left',
    // },
    {
      accessorKey: 'actionCol',
      header: 'Action',
      cell: ({ row }) => (
        <div
          className={styles.editIcon}
          onClick={() => {
            if (router?.query?.search) {
              router.push(`${Routes.EDIT_STATUS}/${row.id}?search=${router?.query?.search}`)
            } else {
              router.push(`${Routes.EDIT_STATUS}/${row.id}`)
            }
          }}
          role="button"
        >
          <EditIcon fill={WHITE} />
        </div>
      ),
      size: 60,
    },
    { accessorKey: 'sortingOrder', header: 'ID', size: 100 },
    {
      accessorKey: 'project_phase_category',
      header: 'Category',
      filterType: 'wildcard-multi-association',
      childAccessorKey: ['LookupProjectToProjectPhaseCategory', 'MasterProjectPhaseCategory'],
      size: 200,
      sortingFn: (...data) => multiValueSorting(...data, 'Multi categories'),
      cell: ({ row }) => {
        // let toolTipMessage =
        //   row.original.project_phase_category?.split(MULTI_SELECT_SEPARATOR)?.length > 1
        //     ? row.original.project_phase_category?.split(MULTI_SELECT_SEPARATOR)?.join(', ')
        //     : ''

        // const tooltipMessageArray =
        //   row.original.project_phase_category?.split(MULTI_SELECT_SEPARATOR)?.filter((res: string) => res) || []

        return (
          <>
            {row.original.LookupProjectToProjectPhaseCategory?.length > 1 ? (
              <div>
                <span className={styles.infoContainer}>
                  <span>Multi categories</span>
                  <span
                    className={styles.infoIcon}
                    ref={categoryAnchorEl}
                    aria-describedby="multi-value-popover"
                    onClick={handleClickCategoryPopover}
                  >
                    <CommonPopoverForDisplayMultiSelect
                      // title="Multi categories"
                      content={prepareMultiPhaseCategoryDropdownOptions(
                        row.original.LookupProjectToProjectPhaseCategory,
                        'MasterProjectPhaseCategory',
                        'project_phase_category',
                      )}
                      maxWidth={350}
                      placement="right"
                    />
                  </span>
                </span>
              </div>
            ) : (
              prepareMultiPhaseCategoryDropdownOptions(
                row.original.LookupProjectToProjectPhaseCategory,
                'MasterProjectPhaseCategory',
                'project_phase_category',
              ).map((item) => item.label)
            )}
          </>
        )
      },
    },
    {
      accessorKey: 'phase',
      header: 'Phase/Package',
      // filterType: 'wildcard-multi-select-phase',
      childAccessorKey: ['LookupProjectToPhase'],
      filterType: 'phase/package',
      size: 200,
      sortingFn: (...data) => multiValueSorting(...data, 'Multi phases'),
      cell: ({ row }) => {
        // Filter valid phases
        const lookupProjectToPhase =
          row.original.LookupProjectToPhase?.filter((p: any) => p.phase && p.phase !== '') || []

        if (lookupProjectToPhase.length > 1) {
          // Multi phases (ignore null/empty)
          return (
            <div>
              <span className={styles.infoContainer}>
                <span>Multi phases</span>
                <span className={styles.infoIcon} aria-describedby="multi-value-popover">
                  <CommonPopoverForDisplayMultiSelect
                    content={prepareDropdownOptions(lookupProjectToPhase, 'phase')}
                    maxWidth={350}
                    placement="right"
                  />
                </span>
              </span>
            </div>
          )
        } else if (lookupProjectToPhase.length === 1) {
          // Single valid phase
          return <>{lookupProjectToPhase[0].phase}</>
        } else {
          // All null/empty
          return <div>{'-'}</div>
        }
      },
      // filterType: 'list',
      // listOption: convertMultiSelectOption(filters.phase),
      require: false,
    },
    {
      accessorKey: 'stageStatus',
      header: 'Stage Status',
      filterType: 'projectStatus',
      filterWithRelation: [{ row: 'subStatus', value: 'design' }],
      size: 240,
      align: 'center',
      require: true,
      filterRow: ['stageStatus', 'subStatus'],
      cell: ({ row }: any) => (
        <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'left' }}>
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'left',
              textAlign: 'left',
            }}
          >
            <span>{row.original.stageStatus}</span>
            <span className={styles.subStatus}>{row.original.subStatus}</span>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'phaseWeightage',
      header: 'Phase Weightage',
      // flex: 1,
      size: 190,
      sortingFn: (...rest) => numericValueSorting(...rest),
      filterType: 'number',
      // align: 'center',
    },
    {
      accessorKey: 'designStageWeightage',
      header: 'Design Stage Weightage',
      // flex: 1,
      size: 190,
      filterType: 'number',
      sortingFn: (...rest) => numericValueSorting(...rest),
      // align: 'center',
    },
    {
      accessorKey: 'Plan/Actual %',
      header: 'Progress %',
      filterType: 'progress',
      require: true,
      isCustomCellEditable: isEditTable,
      isSaveConfirmationRequired: true,
      filterRow: ['actualPlanPercentage', 'revPlanPercentage'],
      cell: (info) => {
        // NOTE : Below calculation is for 'Construction' and 'DLP and Project Closeout' stage , when 'Construction'and 'DLP and Project Closeout' stage, allow user to edit 'revPlanPercentage'
        const stageStatus = info?.row.original.stageStatus
        const isPlanCell = stageStatus === 'Construction' || stageStatus === 'DLP and Project Closeout' ? false : true
        return (
          <div className={styles.progressBar}>
            {[
              // NOTE : Un-comment Below line when allow edit 'revPlanPercentage' for all stage
              // ['revPlanPercentage', false],
              ['revPlanPercentage', isPlanCell],
              ['actualPlanPercentage', false],
            ].map(([columnId, isPlan]) => (
              <ProgressCell
                columnId={columnId}
                disabled={isPlan}
                isPlanCell={isPlan}
                row={info.row}
                val={info.row.original[columnId as string]}
                column={info.column}
                onCellUpdate={(newValue: any) => onCellUpdate({ rowId: info.row.id, columnId }, newValue, info.row)}
              />
            ))}
          </div>
        )
      },
      // flex: 1,
      size: 250,
      align: 'center',
    },
    // TODO: remove this column after testing
    // {
    //   accessorKey: 'calculated_plan_progress',
    //   header: 'Calculated Plan Progress',
    //   size: 250,
    //   // align: 'center',
    //   cell: (info) => {
    //     const { calculated_plan_progress } = info.row.original
    //     return calculated_plan_progress?.toString() ? calculated_plan_progress : 'N/A'
    //   },
    // },
    {
      accessorKey: 'plan_start_date',
      header: 'Plan Start Date',
      filterType: 'date',
      size: 180,
      isSaveConfirmationRequired: true,
      // tableId: 'stageTable',
    },
    {
      accessorKey: 'baselinePlanFinish',
      header: 'Baseline Plan Finish',
      filterType: 'date',
      size: 220,
      isSaveConfirmationRequired: true,
      isCustomCellEditable:
        !hasDesignTeamRole(currentUser) &&
        hasUpdateBaselinePlanFinishDateAndPlanDurationPermission(currentUser) &&
        isEditTable,
      editableType: 'date',
      sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      cell: (info) => {
        const { id, baselinePlanFinish } = info.row.original
        const rowData = getRowIdDataById(id?.toString())
        const isChecked = rowData?.isBaselinePlanFinishEdit === true
        return (
          <DatePickerWithCheckbox
            row={info.row}
            val={baselinePlanFinish}
            column={info.column}
            onCellUpdate={(cell, newValue, row) => onCellUpdate(cell, newValue, row)}
            disabled={!isChecked}
            checked={isChecked}
            onCheckBoxChange={(_event, checked) =>
              updateRowIdDataById(id?.toString(), { isBaselinePlanFinishEdit: checked })
            }
          />
        )
      },
    },
    {
      accessorKey: 'plan_duration',
      header: 'Plan Duration',
      size: 180,
      isSaveConfirmationRequired: true,
      isEditableCell: (_cell, row) => {
        const { id } = row.original
        const rowData = getRowIdDataById(id?.toString())
        const isChecked = rowData?.isBaselinePlanFinishEdit === true
        return hasUpdateBaselinePlanFinishDateAndPlanDurationPermission(currentUser) && isEditTable && !isChecked
      },
      editableType: 'number',
      filterType: 'number',
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
    },
    {
      accessorKey: 'forecast_start_date',
      header: 'Forecast Start Date',
      filterType: 'date',
      size: 180,
      isSaveConfirmationRequired: true,
      sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      // tableId: 'stageTable',
    },
    {
      accessorKey: 'forecastFinish',
      header: 'Forecast Finish',
      filterType: 'date',
      size: 180,
      isSaveConfirmationRequired: true,
      isEditableCell: isEditTable,
      editableType: 'date',
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
      renderInputWithConfirmationMessage: (cell, newValue) => {
        slippageJustification.current = cell?.row?.original?.slippage_justification
        if (
          slippageJustificationValidation(
            cell?.row?.original?.stageStatus,
            newValue,
            cell?.row?.original?.forecastCompletionLastWeek,
          )
        ) {
          return (
            <div className={styles.textEditor}>
              <div className={styles.title}> Slippage Justification </div>
              <RichTextEditor
                value={slippageJustification?.current || ''}
                handleChange={(val) => {
                  return (slippageJustification.current = val)
                }}
                isEdit={true}
                className={styles.textEditor}
              />
            </div>
          )
        }
        return null
      },
      sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      cell: ({ row }) => {
        const forecastFinishDate = row?.original?.forecastFinish ? row?.original?.forecastFinish : ''
        const forecastCompletionLastWeekDate = row?.original?.forecastCompletionLastWeek
          ? row?.original?.forecastCompletionLastWeek
          : '-'
        const highlightField =
          !isEditTable && shouldHighlightForecastDate(forecastFinishDate, forecastCompletionLastWeekDate)
        return (
          <Box
            sx={{
              background: highlightField ? 'yellow' : '',
              padding: '2px 4px',
            }}
            component={'span'}
          >
            {forecastFinishDate}
          </Box>
        )
      },
    },
    {
      accessorKey: 'forecast_duration',
      header: 'Forecast Duration',
      filterType: 'number',
      sortingFn: (...rest) => numericValueSorting(...rest),
      size: 180,
    },
    {
      accessorKey: 'baseLineDiff',
      header: 'Baseline Lead Time',
      size: 170,
      filterType: 'number',
      sortingFn: (...rest) => numericValueSorting(...rest),
      isSaveConfirmationRequired: true,
      isEditableCell: !hasDesignTeamRole(currentUser) && isEditTable,
      editableType: 'number',
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
      visible: currentUser.role?.view_permissions.includes('Edit Lead Functionality'),
    },
    {
      accessorKey: 'forecastDiff',
      header: 'Forecast Lead Time',
      size: 170,
      isSaveConfirmationRequired: true,
      isEditableCell: !hasDesignTeamRole(currentUser) && isEditTable,
      editableType: 'number',
      filterType: 'number',
      sortingFn: (...rest) => numericValueSorting(...rest),
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
      visible: currentUser.role?.view_permissions.includes('Edit Lead Functionality'),
    },
    {
      accessorKey: 'variance',
      header: 'Variance',
      tableId: 'stageTable',
      filterType: 'number',
      sortingFn: (...rest) => numericValueSorting(...rest),
    },
    {
      // BACKEND:AUTO_CALCULATED
      accessorKey: 'forecastCompletionLastWeek',
      header: 'Forecast Completion Last Period ',
      size: 250,
      filterType: 'date',
      visible: true,
      sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
    },
    {
      accessorKey: 'actualLWeek',
      header: 'Actual Last Period',
      size: 160,
      // flex: 1,
      visible: true,
      filterType: 'number',
      tableId: 'stageTable',
      sortingFn: (...rest) => numericValueSorting(...rest),
      // align: 'center',
    },
    {
      accessorKey: 'planLWeek',
      header: 'Plan Last Period',
      size: 160,
      visible: true,
      tableId: 'stageTable',
      filterType: 'number',
      sortingFn: (...rest) => numericValueSorting(...rest),
      // align: 'center',
    },
    {
      accessorKey: 'design_manager',
      header: 'Design Manager',
      visible: false,
      filterType: 'multiSelect',
      // flex: 1,
      // align: 'center',
      // filterType: 'list',
      // listOption: convertAndSortData(convertMultiSelectOption(filters.pmcConsultant)),
    },
    {
      accessorKey: 'procurement_manager',
      header: 'Procurement Manager',
      visible: false,
    },
    {
      accessorKey: 'delivery_project_manager',
      header: 'Delivery PM',
      visible: false,
      filterType: 'multiSelect',
      // flex: 1,
      // align: 'center',
      // filterType: 'list',
      // listOption: convertAndSortData(convertMultiSelectOption(filters.svp)),
    },
    {
      accessorKey: 'contractor',
      header: 'Contractor',
      visible: false,
      // align: 'center',
      // filterType: 'list',
      // listOption: convertAndSortData(convertMultiSelectOption(filters.contractor)),
    },
    {
      accessorKey: 'pmcConsultant',
      header: 'PMC Consultant',
      visible: false,
      // align: 'center',
      // filterType: 'list',
      // listOption: convertAndSortData(convertMultiSelectOption(filters.pmcConsultant)),
    },
  ]

  const handleDrawerClose = () => {
    setValidationModel(null)
    setOpenValidationModel(false)
    setLocalPhaseApi([])
    setIsStageDrawer(false)
    // setLocalCategoryApi([])
  }

  const handleClose = async () => {
    const masterPhase = statuses.filter((item: any) => {
      return item.project_name === router.query.slug
    })
    const validationError = isStageVisited ? await runValidation(masterPhase) : []
    if (validationError?.length) {
      setValidationModel(validationError)
      setOpenValidationModel(true)
    } else {
      handleDrawerClose()
    }
  }

  const applyLeadTime = async (val: number, field: string) => {
    const res = await handleLeadTime(
      val,
      statusData,
      selectRecords,
      updateStatusApi,
      getStatusApi,
      field,
      currentPeriod,
      router?.query?.slug as string,
    )
    setIsLeadModel(null)
    return res
  }

  const hasFilter = useMemo(
    () => gridFilters?.some((item: { colId: string; values: any }) => item.values.length > 0),
    [gridFilters],
  )

  const isLeadButtonDisabled = useMemo(() => {
    return loader || selectRecords.length === 0
  }, [selectRecords, loader])

  const handleClickCategoryIcon = (event: any) => {
    setCategoryAnchorEl(event.currentTarget)
  }

  const handleCloseCategory = () => {
    setCategoryAnchorEl(null)
  }

  const toggleCascadedModal = () => {
    setsCascadedModal(() => !isCascadedModal)
  }

  const handleChangeMultiPhase = async () => {
    if (selectedPhase) {
      setIsSubmittingPhase(true)
      try {
        const payload = {
          projectName: project?.project_name,
          period: currentPeriod,
          phaseId: selectedPhase?.id,
          section: 'projectStatus',
        }
        const res: Record<string, any> = await updateOverallPhaseWithMultiPhaseApi(payload)
        if (res.payload.success) {
          successToast(res?.payload?.message)
          fetchData()
        } else {
          errorToast(res.payload.response.data.message || 'Failed to update phase')
        }
      } catch (error) {
        errorToast('Failed to update overall multi phase')
      } finally {
        setIsSubmittingPhase(false)
      }
    }
  }

  const handleUpdateForecastFinish = async () => {
    setIsForecastUpdating(true)
    const payload = {
      projectName: router.query.slug?.toString() as string,
      period: currentPeriod,
    }
    await updateForecastFinish(payload)
      .then(async (res: any) => {
        toggleCascadedModal()
        setIsForecastUpdating(false)
        if (res?.success) {
          await getStatusApi({
            period: currentPeriod,
            project_name: project?.project_name,
          })
        }
      })
      .catch(() => {
        setIsForecastUpdating(false)
        toggleCascadedModal()
      })
  }

  const isAnyForecastFinishHaveVariance = useMemo(() => {
    return shouldSuccessorHaveDifferentVariance(statusData)
  }, [statusData])

  const onSkip = () => {
    handleDrawerClose()
  }

  const hasOverall = statusData.some((item: any) => {
    return item.phase
      ?.split('$@')
      .map((p: string) => p.trim().toLowerCase())
      .includes('overall')
  })

  return (
    <>
      <div className={styles.contentWrapper}>
        <div className={styles.filterHeader}>
          <div className={styles.filterTab}>
            {currentUser.role?.view_permissions.includes('Status-Add Phase/Status') && (
              <PulseButton
                onClick={() => {
                  if (!isEditForUser) {
                    toast(`The current reporting period is locked`, {
                      icon: <WarningAmberOutlined />,
                    })
                  } else {
                    setIsStageDrawer(true)
                  }
                }}
                label="Add Phase / Stage"
                icon={<AddOutlinedIcon fontSize="large" />}
                disabled={loader}
              />
            )}
            <div className={styles.actionOfTable}>
              {hasFilter ? (
                <Button endIcon={<RestartAlt />} variant="contained" onClick={() => setGridFilters([])}>
                  Reset Filter
                </Button>
              ) : null}
              {/* TODO */}
              {currentUser.role?.view_permissions.includes('Edit Forecast Finish Date') && (
                <>
                  <Button
                    variant="contained"
                    disabled={!isAnyForecastFinishHaveVariance || loader || !isEditForUser}
                    onClick={() => {
                      toggleCascadedModal()
                    }}
                  >
                    Update Forecast Finish
                  </Button>
                </>
              )}
              {currentUser.role?.view_permissions.includes('Edit Lead Functionality') && (
                <>
                  <Button
                    variant="contained"
                    disabled={isLeadButtonDisabled}
                    onClick={() => {
                      setIsLeadModel('Apply Baseline Lead Time')
                    }}
                  >
                    Apply Baseline Lead Time
                  </Button>
                  <Button
                    variant="contained"
                    disabled={isLeadButtonDisabled}
                    onClick={() => {
                      setIsLeadModel('Apply Forecast Lead Time')
                    }}
                  >
                    Apply Forecast Lead Time
                  </Button>
                </>
              )}
              {currentUser.role?.view_permissions.includes('Progress Edit Permission') && (
                <Button
                  variant={isEditTable ? 'outlined' : 'contained'}
                  // className={`${styles.actionsTableBtn} ${isEditTable ? styles.editModeBtn : ''}`}
                  startIcon={<EditNoteIcon />}
                  onClick={() => {
                    if (!isEditForUser) {
                      toast(`The current reporting period is locked`, {
                        icon: <WarningAmberOutlined />,
                      })
                    } else {
                      setIsEditTable(!isEditTable)
                    }
                  }}
                  disabled={loader || !isEditForUser}
                >
                  Edit Table
                </Button>
              )}
              <Button
                startIcon={<EditIcon fill="#FFFFFF" />}
                disabled={loader || !isEditForUser}
                onClick={() => {
                  if (!isEditForUser) {
                    toast(`The current reporting period is locked`, {
                      icon: <WarningAmberOutlined />,
                    })
                  } else {
                    if (statusData.length > 0) {
                      if (router?.query?.search) {
                        router.push(`${Routes.EDIT_STATUS}/${statusData[0].id}?search=${router?.query?.search}`)
                      } else {
                        router.push(`${Routes.EDIT_STATUS}/${statusData[0].id}`)
                      }
                    }
                  }
                }}
              >
                Update Progress
              </Button>
            </div>
          </div>
        </div>
        <PulseModel
          style={{
            position: 'absolute' as 'absolute',
            minWidth: '500px',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: 'fitContent',
            bgcolor: 'background.paper',
            borderRadius: '12px',
            boxShadow: 24,
            pt: '20px',
            px: '20px',
            pb: '20px',
          }}
          open={Boolean(isLeadModel)}
          closable={true}
          onClose={() => setIsLeadModel(null)}
          content={
            <>
              <LeadTimeModel
                isLeadModel={isLeadModel}
                onClose={() => setIsLeadModel(null)}
                applyLeadTime={(val, field) => applyLeadTime(val as number, field)}
              />
            </>
          }
        />
        {loader && !isStageDrawer ? (
          <Loader />
        ) : (
          <div className={`${styles.tableWrapper} ${!expanded && styles.expandTable}`}>
            <TanStackTable
              rows={statusData as any}
              columns={columns}
              onDragEnd={handleDragAndDrop}
              columnVisibilities={progressTableVisibilities}
              setColumnVisibilitiesApi={setProgressTableColumnVisibilitiesApi}
              gridFilters={gridFilters} // Persist filter data across refreshes and change projects using Redux state. Stored in local storage for one-time setup.
              setGridFilters={setGridFilters}
              stickyColumnCount={currentUser.role?.view_permissions.includes('Edit Lead Functionality') ? 7 : 6}
            />
          </div>
        )}
        <div className={styles.phaseStageDrawer}>
          <Drawer anchor="right" open={isStageDrawer} onClose={() => handleClose()} paperSx={{ overflow: 'hidden' }}>
            {isStageDrawer && (
              <PhaseAndStageTab
                isStageDrawer={isStageDrawer}
                onClose={() => handleClose()}
                setIsStageVisited={setIsStageVisited}
                isStageVisited={isStageVisited}
              />
            )}
          </Drawer>
        </div>
        <PulseModel
          closable={false}
          style={{ width: 'fitContent' }}
          open={isValidationModel}
          onClose={() => setIsValidationModel(false)}
          content={<ValidationModel messages={validationMessage} onClose={() => setIsValidationModel(false)} />}
        />
        <ValidationError
          validationModel={validationModel}
          open={openValidationModel}
          onClose={() => {
            setOpenValidationModel(false)
            setTimeout(() => {
              setValidationModel(null)
            }, 0)
          }}
          handleConfirm={() => {}}
          onSkip={onSkip}
        />

        {/* add a popup for multiphase and multiCategory display */}
        <ConfirmationModal
          open={isCascadedModal}
          onClose={toggleCascadedModal}
          handleConfirm={handleUpdateForecastFinish}
          loading={isForecastUpdating}
          message="This will update the successor forecast finish dates. Do you want to proceed?"
        />
        {/* //TODO: Commented by merging stageing to revamp. */}
        {/*  <ReplaceMultiPhaseModal
          open={isReplaceMultiPhaseModal}
          onClose={multiphaseModal}
          handleConfirm={handleChangeMultiPhase}
          selectedPhase={selectedPhase}
          setSelectedPhase={setSelectedPhase}
          isSubmittingPhase={isSubmittingPhase}
        /> */}
      </div>
    </>
  )
}

export default StatusTab
