import React, { useCallback, useMemo } from 'react'
import styles from './BannerTabs.module.scss'
import Tab from '../../tab'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useDataEntryScreen from '@/src/redux/dataEntryScreen/useDataEntryScreen'

const tabsConfig = [
  { permission: 'Summary', key: 'Summary', name: 'Summary' },
  { permission: 'Status', key: 'Progress', name: 'Progress' },
  { permission: 'Commercials', key: 'Commercials', name: 'Commercials' },
  { permission: 'Project Overview', key: 'Project Overview', name: 'Project Overview' },
  { permission: 'SPA & Milestones', key: 'SPA & Milestones', name: 'SPA & Milestones' },
  {
    permission: 'Authority Approvals Tracking',
    key: 'Authority Approvals Tracking',
    name: 'Authority Approvals Tracking',
  },
  { permission: 'Handover', key: 'Handover', name: 'Handover' },
  { permission: 'Media', key: 'Media', name: 'Media' },
]

const BannerTabs = () => {
  const { currentUser } = useAuthorization()
  const { selectedTab, setSelectedTabApi } = useDataEntryScreen()

  const filteredTabs = useMemo(
    () => tabsConfig.filter((tab) => currentUser?.role?.view_permissions?.includes(tab.permission)),
    [currentUser],
  )

  const handleTabSelect = useCallback((tabName: string) => setSelectedTabApi(tabName), [setSelectedTabApi])

  return (
    <>
      <div className={styles.tabContainer}>
        <div className={`${styles.tabLayoutContainer} `}>
          <div className={styles.tabButtons}>
            {filteredTabs.map(
              (tab) =>
                currentUser?.role?.view_permissions?.includes(tab.permission) && (
                  <Tab key={tab.key} name={tab.name} onSelect={handleTabSelect} isActive={selectedTab === tab.name}>
                    {tab.name}
                  </Tab>
                ),
            )}
          </div>
        </div>
      </div>
    </>
  )
}

export default BannerTabs
