import { InfoOutlined } from '@mui/icons-material'
import { Box, InputAdornment, Tooltip } from '@mui/material'
import { format, isValid, parse } from 'date-fns'
import styles from './CollapseField.module.scss'
import CollapseDatePicker from './collpaseDatePicker'
import { ICollapseField } from './interface'
import RichTextEditor from '../../richTextEditor'
import Checkbox from '../../shared/checkbox'
import ComboBox from '../../shared/combobox'
import DropdownMenuButton from '../../shared/dropDownMenu'
import MultiAutoSelect from '../../shared/multiAutoSelect'
import NumberInputField from '../../shared/numberInputField'
import Textarea from '../../shared/textArea'
import TextInputField from '../../shared/textInputField'
import TypographyField from '../../shared/typography'
import AedIcon from '../../svgImages/aedIcon'
import { convertMultiSelectOption, getValue } from '@/src/utils/arrayUtils'
import { formatNumberWithCommas } from '@/src/utils/numberUtils'

const REGEX = /^[0-9]*(\.[0-9]*)?$/

const NUMBER_INPUT_STYLE = {
  '& .Mui-focused': {
    background: '#f4f4fc',
    '& .MuiOutlinedInput-notchedOutline': {},
  },
}

const FieldLabel = ({ label, labelStyle }: any) => (
  <TypographyField
    className={`${styles.fieldLabel} ${labelStyle} `}
    variant="caption"
    style={{ color: '#808080' }}
    text={label}
  />
)

const RichTextLabel = ({ label, labelStyle, summaryEdit }: any) => (
  <div
    className={`${styles.richTextLabel} ${styles.fieldLabel} ${labelStyle}  ${summaryEdit ? styles.borderBottomNone : ''}`}
  >
    <TypographyField variant="caption" style={{ color: '#808080' }} text={label} />
    <div>
      <Tooltip
        title={
          <Box>
            <span className={styles.tooltipText}>Ctrl + B to make text bold.</span>
            <br />
            <span className={styles.tooltipText}>Ctrl + I to italicize text.</span>
            <br />
            <span className={styles.tooltipText}>Ctrl + Z to undo </span>
            <br />
            <span className={styles.tooltipText}>Ctrl + Y to redo.</span>
            <br />
            <span className={styles.tooltipText}>Ctrl + Shift + 8 for a bullet list.</span>
            <br />
            <span className={styles.tooltipText}>Ctrl + Shift + 7 for a numbered list.</span>
          </Box>
        }
        arrow
      >
        <InfoOutlined />
      </Tooltip>
    </div>
  </div>
)

const TextAreaField = ({ formik, fieldName, disabled, isNumber }: any) => (
  <Textarea
    cols={2}
    className={styles.textArea}
    name={fieldName}
    disabled={disabled}
    value={formik.values && formik.values[fieldName]}
    onChange={(e) => {
      e.stopPropagation()
      const inputValue = e.target.value
      if (isNumber) {
        const isValidInput = REGEX.test(inputValue)
        if (isValidInput || inputValue === '') {
          return formik.setFieldValue(fieldName, e.target.value)
        }
      } else {
        formik.setFieldValue(fieldName, e.target.value)
      }
    }}
    error={formik.touched[fieldName] && !!formik.errors[fieldName]}
    helperText={formik.touched[fieldName] && formik.errors[fieldName] ? formik.errors[fieldName] : ''}
    onBlur={formik.handleBlur}
  />
)

const NumberInput = ({ formik, fieldName, disabled, className, maxLength }: any) => (
  <NumberInputField
    isUpAndDowns={false}
    name={fieldName}
    className={`${disabled ? styles.inputField : styles.highlightField}  ${className}`}
    value={formik.values[fieldName] ? formatNumberWithCommas(formik.values[fieldName]?.toLocaleString()) : ''}
    format="comma-separated"
    onChange={(value) => formik.setFieldValue(fieldName, value)}
    onBlur={formik.handleBlur}
    sx={NUMBER_INPUT_STYLE}
    maxLength={maxLength}
    disabled={disabled}
    error={formik.touched[fieldName] && !!formik.errors[fieldName]}
    helperText={formik.touched[fieldName] && formik.errors[fieldName] ? formik.errors[fieldName] : ''}
  />
)

const ComboBoxField = ({ formik, fieldName, disabled, options, clearIcon, isCustomSorting }: any) => (
  <ComboBox
    className={`${disabled ? styles.disableComboBox : styles.comboBox}`}
    options={options}
    clearIcon={clearIcon}
    disabled={disabled}
    focusCustomClass={styles.focusCompoBox}
    value={
      Array.isArray(formik.values[fieldName])
        ? getValue(options, formik.values[fieldName][0])
        : formik.values && formik.values[fieldName]
          ? getValue(options, formik.values[fieldName]) || {
              label: formik.values[fieldName],
              value: formik.values[fieldName],
            }
          : null
    }
    onChange={(selectedOption) => {
      formik.setFieldValue(fieldName, selectedOption ? selectedOption.value : '')
    }}
    onBlur={() => formik.setTouched({ ...formik.touched, [fieldName]: true })}
    error={formik.touched[fieldName] && !!formik.errors[fieldName]}
    helperText={formik.touched[fieldName] && formik.errors[fieldName] ? formik.errors[fieldName] : ''}
    isCustomSorting={isCustomSorting}
  />
)

const DatePickerField = ({
  dateWidth,
  formik,
  fieldName,
  minDate,
  disabled,
  isOpen,
  handleDatePickerOpen,
  handleDatePickerClose,
  className,
  disableWeekends,
  isShowBottomBorder,
}: any) => {
  const convertToYYYYMMDD = (value: any) => {
    // If value is falsy, return early
    if (!value) return ''
    // If value is a Date object, format it directly
    if (value instanceof Date) {
      return format(value, 'yyyy-MM-dd')
    }
    // If value is a string, try parsing it from 'dd-MM-yyyy' format
    const parsedDate = parse(value, 'dd-MM-yyyy', new Date())
    // Check if the parsed date is valid
    if (!isValid(parsedDate)) {
      return ''
    }
    // Format the parsed date to 'yyyy-MM-dd'
    return format(parsedDate, 'yyyy-MM-dd')
  }

  return (
    <CollapseDatePicker
      open={isOpen}
      minDate={minDate}
      onOpen={handleDatePickerOpen}
      onClose={handleDatePickerClose}
      className={`${styles.datePicker} ${disabled && styles.disableDatePicker} ${className} ${disabled && !isShowBottomBorder ? styles.borderBottomNone : ''}`}
      name={fieldName}
      placeholder="DD/MM/YY"
      disabled={disabled}
      value={formik.values[fieldName.toString()] && convertToYYYYMMDD(formik.values[fieldName.toString()])}
      onChange={(value: any) => {
        if (value == 'Invalid Date') {
          formik.setFieldValue(fieldName, null)
          return
        }
        formik.setFieldValue(fieldName, value ? value : null)
      }}
      onBlur={formik.handleBlur}
      error={!!formik.errors[fieldName] && formik.touched[fieldName]}
      helperText={
        formik.touched[fieldName] && typeof formik.errors[fieldName] === 'string' ? formik.errors[fieldName] : ''
      }
      disableWeekends={disableWeekends}
      sx={{
        '& .MuiOutlinedInput-root': {
          // width: `${dateWidth}`,
          backgroundColor: '#f0f8ff',
          borderBottom: '1px solid rgb(40 101 220)',
          borderTop: 0,
          borderRight: 0,
          borderLeft: 0,
          borderRadius: 0,
          minHeight: '38px',
        },
        '& .MuiOutlinedInput-input': {
          padding: '8px 0px 6px 8px',
          fontWeight: '400',
          fontSize: '12px',
          lineHeight: '18px',
          borderBottom: '0px solid rgb(40 101 220)',
        },
        '& .Mui-disabled': {
          backgroundColor: '#FFFFFF !important',
          borderBottom: '1px solid #FFFFFF !important',
          borderRadius: '0px',
          '& .MuiOutlinedInput-root': {
            // backgroundColor: '#f4f4fc',
          },
        },
      }}
    />
  )
}

const CheckBoxField = ({ formik, fieldName, isEdit, onChange }: any) => {
  return (
    <div className={` ${!isEdit ? styles.checkBoxBorder : styles.checkBox}`}>
      <Checkbox
        checked={formik.values[fieldName] ? true : false}
        disabled={!isEdit}
        onChange={(e) => {
          e.stopPropagation()
          onChange
            ? onChange(e.target.checked)
            : formik.setFieldValue(
                fieldName,
                fieldName === 'is_executive_project' ? (e.target.checked ? 1 : 0) : e.target.checked,
              )
        }}
        className={` ${!isEdit ? styles.checkBoxBorder : styles.checkBox}`}
      />
    </div>
  )
}

const MultiSelectField = ({ formik, fieldName, options, className }: any) => {
  const seperator = ','
  let value =
    formik.values && formik.values[fieldName] && typeof formik.values[fieldName] === 'string'
      ? formik.values[fieldName].split(seperator)?.map((res: any) => res?.trim())
      : []
  // NOTE : When value get as JSON Array Format, add that fieldName in fieldWithJSON array
  const fieldWithJSON = [
    'non_recoverable_delay_justification_ids',
    'next_steps_to_advance_progress_ids',
    'mitigation_recovery_plan_ids',
    'primary_reason_for_delay_ids',
    'reason_for_delay_ids',
    'sub_location_ids',
  ]
  if (fieldWithJSON?.includes(fieldName)) {
    value = formik.values && formik.values[fieldName] ? formik.values[fieldName] : []
  }

  return (
    <>
      <MultiAutoSelect
        className={`${styles.multiSelectField} ${className}`}
        options={convertMultiSelectOption(options)}
        value={value}
        handleSelectedOption={(selectedOptions) =>
          fieldWithJSON?.includes(fieldName)
            ? formik.setFieldValue(
                fieldName,
                selectedOptions.map((option) => option),
              )
            : formik.setFieldValue(fieldName, selectedOptions.map((option) => option).join(seperator))
        }
      />
    </>
  )
}

const DropdownMenuField = ({ formik, fieldName, buttonLabel, menuItems, selectedValue, onDropDownMenuChange }: any) => (
  <DropdownMenuButton
    buttonLabel={buttonLabel}
    menuItems={menuItems}
    selectedValue={selectedValue}
    onChange={(value) => {
      formik.setFieldValue(fieldName, value)
      onDropDownMenuChange && onDropDownMenuChange(value)
    }}
  />
)

const TextInputFieldWrapper = ({ formik, fieldName, disabled, isNumber, endAdornment }: any) => (
  <TextInputField
    name={fieldName}
    variant="outlined"
    disabled={disabled}
    className={`${disabled ? styles.disableTextField : styles.textField}`}
    onChange={(e) => {
      e.stopPropagation()
      const inputValue = e.target.value
      if (isNumber) {
        const isValidInput = REGEX.test(inputValue)
        if (isValidInput || inputValue === '') {
          return formik.setFieldValue(fieldName, e.target.value)
        }
      } else {
        formik.setFieldValue(fieldName, e.target.value)
      }
    }}
    value={formik.values && formik.values[fieldName]}
    error={formik.touched[fieldName] && !!formik.errors[fieldName]}
    helperText={formik.touched[fieldName] && formik.errors[fieldName] ? formik.errors[fieldName] : ''}
    onBlur={formik.handleBlur}
    InputProps={{ endAdornment }} // Pass endAdornment to TextInputField
    sx={{
      '& .Mui-focused': {
        backgroundColor: '#f0f8ff !important',
        borderBottom: '1px solid rgb(40 101 220) !important',
        '& .MuiOutlinedInput-input': {
          backgroundColor: '#f4f4fc',
        },
      },
    }}
  />
)

const getColorCode = (type: 'error' | 'default'): string => {
  switch (type) {
    case 'error':
      return '#FF4D4F'
    case 'default':
      return '#444444'
    default:
      return '#000000'
  }
}

export const CollapseField: React.FC<ICollapseField> = ({
  dateWidth,
  dotStyle,
  valueStyle,
  labelStyle,
  label,
  value,
  summaryEdit,
  formik,
  fieldName,
  isTextArea,
  isTextEditor,
  buttonLabel,
  menuItems,
  selectedValue,
  onDropDownMenuChange,
  isNumber,
  isComboBox,
  isNumberField,
  options,
  minDate,
  isOpen,
  handleDatePickerOpen,
  handleDatePickerClose,
  isDate,
  isMultiSelect,
  disabled,
  isCheckBox,
  idDropDownButton,
  endAdornment,
  onCheckBoxChange,
  className,
  rootClassName,
  disableWeekends = false,
  isShowBottomBorder = false,
  clearIcon = true,
  maxLength,
  isCustomSorting = false,
  textColor = 'default',
}) => {
  return (
    <div className={`${styles.field} ${rootClassName ? rootClassName : ''}`}>
      {isTextEditor ? (
        <RichTextLabel label={label} summaryEdit={summaryEdit} />
      ) : (
        <FieldLabel label={label} labelStyle={labelStyle} />
      )}

      <div className={styles.fieldValue}>
        <div className={`${dotStyle ? dotStyle : styles.dot}`}>:</div>
        {summaryEdit ? (
          <>
            {isTextEditor && (
              <RichTextEditor
                value={formik.values && formik.values[fieldName]}
                handleChange={(val) => formik.setFieldValue(fieldName, val)}
                isEdit={summaryEdit}
                className={`${styles.textEditorValue} ${className}`}
              />
            )}
            {isNumberField && (
              <NumberInput
                formik={formik}
                fieldName={fieldName}
                maxLength={maxLength}
                disabled={disabled}
                className={className}
              />
            )}
            {isTextArea && (
              <TextAreaField formik={formik} fieldName={fieldName} disabled={disabled} isNumber={isNumber} />
            )}
            {isComboBox && (
              <ComboBoxField
                formik={formik}
                fieldName={fieldName}
                disabled={disabled}
                options={options}
                clearIcon={clearIcon}
                isCustomSorting={isCustomSorting}
              />
            )}
            {isDate && (
              <DatePickerField
                dateWidth={dateWidth}
                formik={formik}
                fieldName={fieldName}
                disabled={disabled}
                isOpen={isOpen}
                minDate={minDate}
                handleDatePickerOpen={handleDatePickerOpen}
                handleDatePickerClose={handleDatePickerClose}
                className={className}
                disableWeekends={disableWeekends}
                isShowBottomBorder={isShowBottomBorder}
              />
            )}
            {isCheckBox && (
              <CheckBoxField formik={formik} fieldName={fieldName} isEdit={true} onChange={onCheckBoxChange} />
            )}
            {isMultiSelect && (
              <MultiSelectField formik={formik} fieldName={fieldName} options={options} className={className} />
            )}
            {idDropDownButton && (
              <DropdownMenuField
                formik={formik}
                fieldName={fieldName}
                buttonLabel={buttonLabel}
                menuItems={menuItems}
                selectedValue={selectedValue}
                onDropDownMenuChange={onDropDownMenuChange}
              />
            )}
            {!isTextArea &&
              !isComboBox &&
              !isDate &&
              !isCheckBox &&
              !isMultiSelect &&
              !idDropDownButton &&
              !isTextEditor &&
              !isNumberField && (
                <TextInputFieldWrapper
                  formik={formik}
                  fieldName={fieldName}
                  disabled={disabled}
                  isNumber={isNumber}
                  endAdornment={endAdornment}
                />
              )}
          </>
        ) : (
          <>
            {isCheckBox ? (
              <CheckBoxField formik={formik} fieldName={fieldName} isEdit={false} />
            ) : (
              <>
                {isTextEditor ? (
                  <RichTextEditor
                    value={formik.values && formik.values[fieldName]}
                    handleChange={(val) => formik.setFieldValue(fieldName, val)}
                    isEdit={summaryEdit}
                    className={`${styles.textEditorValue} ${className}`}
                    isShowDisableBorder={false}
                  />
                ) : (
                  <TypographyField
                    className={isTextArea ? styles.textAreaValue : valueStyle ? valueStyle : styles.value}
                    variant="caption"
                    style={{ color: getColorCode(textColor) }}
                    text={value?.toString()}
                  />
                )}
              </>
            )}
          </>
        )}
      </div>
    </div>
  )
}
