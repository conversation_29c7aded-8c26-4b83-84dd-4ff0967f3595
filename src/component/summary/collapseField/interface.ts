export interface ICollapseField {
  dotStyle?: string
  valueStyle?: string
  dateWidth?: string
  labelStyle?: string
  label: string
  value: string
  summaryEdit: boolean
  formik: any
  fieldName: string
  isTextArea?: boolean
  isTextEditor?: boolean
  isNumber?: boolean
  minDate?: any
  isComboBox?: boolean
  isCheckBox?: boolean
  isNumberField?: boolean
  disabled?: boolean
  options?: any
  isOpen?: any
  handleDatePickerOpen?: any
  handleDatePickerClose?: any
  isDate?: boolean
  idDropDownButton?: boolean
  isMultiSelect?: boolean
  endAdornment?: React.ReactNode
  buttonLabel?: string
  menuItems?: string[]
  selectedValue?: string // New prop for the selected value
  onDropDownMenuChange?: (value: string) => void // Callback for onChange event
  onCheckBoxChange?: (value: boolean) => void // Callback for checkbox change
  className?: string
  rootClassName?: string
  disableWeekends?: boolean
  isShowBottomBorder?: boolean
  clearIcon?: boolean
  maxLength?: number
  isCustomSorting?: boolean
  textColor?: 'error' | 'default'
}
