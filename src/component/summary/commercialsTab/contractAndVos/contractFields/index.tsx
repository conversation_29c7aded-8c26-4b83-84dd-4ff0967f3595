import React, { useEffect, useMemo, useState } from 'react'
import { Box, InputAdornment } from '@mui/material'
import Tooltip from '@mui/material/Tooltip'
import { useRouter } from 'next/router'
import styles from './ContractFields.module.scss'
import Checkbox from '@/src/component/shared/checkbox'
import ComboBox from '@/src/component/shared/combobox'
import CustomMultiAutoSelect from '@/src/component/shared/multiAutoSelect/customMultiAutoSelect'
import MultiSearchAutoSelect from '@/src/component/shared/multiAutoSelect/multiAutoSelect'
import NumberInputField from '@/src/component/shared/numberInputField'
import TextInput<PERSON>ield from '@/src/component/shared/textInputField'
import ToggleButton from '@/src/component/shared/toggleButton'
import AedIcon from '@/src/component/svgImages/aedIcon'
import { MULTI_SELECT_SEPARATOR } from '@/src/constant/stageStatus'
import { convertValuesToCommaSeparated, getUniquePhases } from '@/src/helpers/helpers'
import { useGetConsultant } from '@/src/hooks/useConsultant'
import { useGetContractor } from '@/src/hooks/useContractor'
import { useGetContractType } from '@/src/hooks/useContractType'
import { useGetPmcConsultant } from '@/src/hooks/usePmcConsultant'
import useCategory from '@/src/redux/category/useCategory'
import useStatus from '@/src/redux/status/useStatus'
import {
  convertMultiSelectOption,
  getOptionsValue,
  getUniqueValuesById,
  getUniqueValuesFromArray,
  getValue,
  getValueForMultiSelectOption,
  getValues,
  populateDropdownOptions,
  prepareDropdownOptions,
  prepareMultiPhaseCategoryDropdownOptions,
} from '@/src/utils/arrayUtils'
import { formatNumberWithCommas } from '@/src/utils/numberUtils'

const NUMBER_INPUT_STYLE = {
  '& .Mui-focused': {
    background: '#f4f4fc',
    // border: '1px solid #2333C2BF',
  },
  '& .Mui-disabled': {
    color: 'red !important', // Ensure the color for the disabled state
  },
}

const contractorCheckBoxSx = {
  display: 'flex',
  alignItems: '',
  gap: '10px',
  '& .contractorCheckbox': {
    marginTop: '25px',
    backgroundColor: '#f0f8ff', // light blue or any color you prefer
    border: '1px solid #2865dc',
    borderRadius: '3px',
    padding: 0,
    height: 'fit-content',
    // alignItems: 'center',
  },
  '& .disabledCheckbox': {
    marginTop: '18px',
  },
}

interface ContractFieldsProps {
  formik: any
  isEditMode: boolean
}

const ContractFields: React.FC<ContractFieldsProps> = ({ formik, isEditMode }) => {
  const router = useRouter()
  const { statuses } = useStatus()
  // TODO : test all below
  const { contractTypes } = useGetContractType()
  const { contractors } = useGetContractor()
  const { consultants } = useGetConsultant()
  const { pmcConsultants } = useGetPmcConsultant()
  const { localCategory } = useCategory()

  const [filterOptions, setFilterOptions] = useState<any>({
    phase: '',
  })

  const contractTypeOptions = useMemo(() => {
    // return populateDropdownOptions(contractTypes, 'contract_type')
    return prepareDropdownOptions(contractTypes, 'contract_type')
  }, [contractTypes])

  const phaseOptions: any[] = useMemo(() => {
    return populateDropdownOptions(
      statuses.filter((item: any) => {
        return item.project_name === router.query.slug
      }),
      'LookupProjectToPhase',
    )
  }, [statuses])

  // TODO: Old Code
  // const contractorsOptions = useMemo(() => {
  //   return prepareDropdownOptions(contractors, 'contractor')
  //   // return populateDropdownOptions(contractors, 'contractor')
  // }, [contractors])

  // const consultantsOptions = useMemo(() => {
  //   // return populateDropdownOptions(consultants, 'consultant')
  //   return prepareDropdownOptions(consultants, 'consultant')
  // }, [consultants])

  const { contractorsOrConsultantsOptions, noOptionsMessage } = useMemo(() => {
    const contractTypeLabel = getValue(contractTypeOptions, formik.values?.master_contract_type_id)?.label

    let options: any = []
    let noOptionsMessage: string = ''

    switch (contractTypeLabel) {
      case 'Construction':
        options = prepareDropdownOptions(contractors, 'contractor')?.map((item) => {
          return { ...item, value: `contractor-${item.value.toString()}` }
        })
        break
      case 'Consultancy':
        options = prepareDropdownOptions(consultants, 'consultant')
        break
      case 'Supervision':
        options = prepareDropdownOptions(pmcConsultants, 'pmc_consultant')?.map((item) => {
          return { ...item, value: `pmc-${item.value.toString()}` }
        })
        break
      default:
        options = []
        noOptionsMessage = 'Please select Contract Type.'
        break
    }

    /*  {
        label: 'Contractor',
        options: [...prepareDropdownOptions(contractors, 'contractor')]?.map((item) => {
          return { ...item, value: `contractor-${item.value.toString()}` }
        }),
      },
      {
        label: 'Consultant',
        options: [...prepareDropdownOptions(consultants, 'consultant')]?.map((item) => {
          return item
        }),
      }, */

    return { contractorsOrConsultantsOptions: options, noOptionsMessage }
  }, [consultants, contractors, formik.values?.master_contractor_id, formik.values?.master_contract_type_id])

  const { values } = formik

  const phaseDisplayOptions = useMemo(() => {
    const uniquePhases = getUniqueValuesById(phaseOptions.flat())
    return uniquePhases
      ?.map((item: any) => {
        return item?.phase
          ? {
              label: item?.phase,
              value: item?.id,
            }
          : null
      })
      .filter((item: any) => !!item?.label)
  }, [phaseOptions])

  const projectCategoryOption: any = useMemo(() => {
    const optionMap = new Map()
    statuses.forEach((item: any) => {
      if (item.project_phase_category?.includes(MULTI_SELECT_SEPARATOR)) {
        const multiCategories = item.project_phase_category.split(MULTI_SELECT_SEPARATOR)
        multiCategories.forEach((category: any) => {
          optionMap.set(category, { id: category, name: category })
        })
      } else {
        optionMap.set(item.project_phase_category, {
          id: item.project_phase_category,
          name: item.project_phase_category,
        })
      }
    })
    localCategory.forEach((item: any) => {
      if (item.category?.includes(MULTI_SELECT_SEPARATOR)) {
        const multiCategories = item.category.split(MULTI_SELECT_SEPARATOR)
        multiCategories.forEach((category: any) => {
          optionMap.set(category, { id: category, name: category })
        })
      } else {
        optionMap.set(item.category, { id: item.category, name: item.category })
      }
    })
    return Array.from(optionMap.values()).filter((item: any) => item.name?.length)
  }, [statuses, localCategory])

  const multiPhaseOptions = useMemo(() => {
    return phaseDisplayOptions?.map((item: any) => {
      return {
        id: item?.value,
        name: item?.label,
      }
    })
  }, [phaseDisplayOptions])

  // TODO: Commented old code
  // const currentPhaseValue = useMemo(() => {
  //   let data = formik.values.project_to_project_phase_ids
  //     ? getValueForMultiSelectOption(
  //         convertMultiSelectOption(phaseDisplayOptions),
  //         formik.values.project_to_project_phase_ids,
  //       )
  //     : null
  //   return data
  // }, [formik.values?.project_to_project_phase_ids])

  const getMultiSelectedValue = (value: string) => {
    if (value?.includes(MULTI_SELECT_SEPARATOR)) {
      return value.split(MULTI_SELECT_SEPARATOR)
    }
    return typeof value === 'string' ? [value] : value
  }

  const handleMultiAutoSelectChange = (value: string[], fieldName: 'project_to_project_phase_ids') => {
    const ids = value.map((item) => item).filter((id) => id !== null && id)

    formik.setValues({
      ...formik.values,
      [fieldName]: Array.from(new Set(ids)),
    })
  }

  // const handleButttonToggol = (_event: any, checked: boolean) => {
  //   formik?.setValues({
  //     ...formik.values,
  //     is_contractor_edit: checked,
  //     master_consultant_id: null,
  //     master_contractor_id: null,
  //   })
  // }

  return (
    <div>
      <div className={styles.content}>
        {/* Description */}
        {/* <TextInputField
          name="phase"
          labelText={"Phase"}
          placeholder="Phase"
          variant={"outlined"}
          onChange={formik.handleChange}
          value={formik.values.phase}
          onBlur={formik.handleBlur}
          sx={NUMBER_INPUT_STYLE}
          disabled={!isEditMode}
        /> */}
        <div className={styles.upGrid}>
          {/* <Tooltip title={currentPhaseValue?.length > 0 ? convertValuesToCommaSeparated(currentPhaseValue) : ''} arrow> */}
          <div>
            {/* <ComboBox
                options={phaseDisplayOptions as any}
                labelText={'Phase'}
                clearIcon={true}
                // placeholder="Type Something..."
                className={`${!isEditMode && styles.isActive}  ${!isEditMode ? styles.comboBoxInput : styles.comboHighlight}`}
                value={
                  formik.values?.phase
                    ? {
                        label: convertValuesToCommaSeparated(formik.values?.phase),
                        value: formik.values?.phase,
                      }
                    : null
                }
                onChange={(val) =>
                  formik.setValues({
                    ...formik.values,
                    phase: val?.value || '',
                  })
                }
                disabled={!isEditMode}
              /> */}

            <Tooltip
              title={
                formik.values.project_to_project_phase_ids
                  ? getValues(phaseDisplayOptions, formik.values.project_to_project_phase_ids).join(', ')
                  : null
              }
              arrow
            >
              <div>
                {/* <CustomMultiAutoSelect
                  value={getValueForMultiSelectOption(
                    convertMultiSelectOption(phaseDisplayOptions),
                    formik.values.project_to_project_phase_ids,
                  )}
                  isSubOption={false}
                  options={convertMultiSelectOption(phaseDisplayOptions).filter(
                    (item: any) => item.id !== null && item.name !== null,
                  )}
                  disabled={!isEditMode}
                  labelText={'Phase'}
                  clearIcon={true}
                  className={`${!isEditMode && styles.isActive}  ${!isEditMode ? styles.comboBoxInput : styles.comboHighlight}`}
                  handleSelectedOption={(val) => handleMultiAutoSelectChange(val, 'project_to_project_phase_ids')}
                  isMultiFilterLabel={true}
                  multiFilterLabelText={'Phase'}
                  inputValue={filterOptions?.project_to_project_phase_ids}
                  setInputValue={(val: any) =>
                    setFilterOptions({ ...filterOptions, project_to_project_phase_ids: val })
                  }
                /> */}
                <MultiSearchAutoSelect
                  labelText={'Phase'}
                  isSubOption={false}
                  isSx={false}
                  clearIcon={true}
                  options={convertMultiSelectOption(phaseDisplayOptions)}
                  value={getMultiSelectedValue(formik.values.project_to_project_phase_ids) as unknown as string[]}
                  disabled={!isEditMode}
                  handleSelectedOption={(val) => handleMultiAutoSelectChange(val, 'project_to_project_phase_ids')}
                  className={`${!isEditMode && styles.isActive}  ${!isEditMode ? styles.comboBoxInput : styles.comboHighlight}`}
                  inputValue={filterOptions?.project_to_project_phase_ids}
                  setInputValue={(val: any) =>
                    setFilterOptions({ ...filterOptions, project_to_project_phase_ids: val })
                  }
                />
              </div>
            </Tooltip>
          </div>
          {/* </Tooltip> */}
          <Tooltip
            title={
              formik.values?.master_contract_type_id
                ? getValue(contractTypeOptions, formik.values?.master_contract_type_id)?.label
                : null
            }
            arrow
          >
            <div>
              <ComboBox
                options={contractTypeOptions}
                labelText={'Contract Type'}
                clearIcon={true}
                // placeholder="Type Something..."
                className={`${!isEditMode && styles.isActive} ${styles.comboBoxInput} ${!isEditMode ? '' : styles.comboHighlight}`}
                value={
                  formik.values?.master_contract_type_id
                    ? getValue(contractTypeOptions, formik.values?.master_contract_type_id)
                    : null
                }
                onChange={(val) => {
                  // If master_contractor_id is set, clear it when changing contract type
                  formik?.setValues({
                    ...formik?.values,
                    master_contract_type_id: val?.value,
                    master_contractor_id: null,
                    master_consultant_id: null,
                    master_pmc_consultant_id: null,
                  })
                }}
                disabled={!isEditMode}
              />
            </div>
          </Tooltip>
          <Tooltip
            title={
              formik.values?.master_contractor_id
                ? getValue(contractorsOrConsultantsOptions, formik.values?.master_contractor_id)?.label
                : null
            }
            arrow
          >
            <div>
              <ComboBox
                options={contractorsOrConsultantsOptions}
                labelText="Contractor/Consultant"
                clearIcon={true}
                // placeholder="Type Something..."
                className={`${!isEditMode && styles.isActive} ${styles.comboBoxInput} ${!isEditMode ? '' : styles.comboHighlight}`}
                value={
                  formik.values?.master_contractor_id
                    ? getValue(contractorsOrConsultantsOptions, formik.values?.master_contractor_id)
                    : null
                }
                onChange={(val) => formik.setFieldValue('master_contractor_id', val?.value || '')}
                disabled={!isEditMode}
                noOptionsMessage={noOptionsMessage}
              />
            </div>
          </Tooltip>
          <Tooltip title={formik.values.description} arrow>
            <div>
              <TextInputField
                name="description"
                labelText={'Description'}
                className={`${!isEditMode ? '' : styles.highlightField} ${styles.inputField}`}
                // placeholder="Description"
                variant={'outlined'}
                // classes={{ root: styles.inputField }}
                onChange={formik.handleChange}
                value={formik.values.description}
                onBlur={formik.handleBlur}
                sx={NUMBER_INPUT_STYLE}
                disabled={!isEditMode}
              />
            </div>
          </Tooltip>

          {/* <div></div> */}
        </div>
        {/* original_contract_value */}
        <div className={styles.dowGrid}>
          <Tooltip
            title={
              values?.original_contract_value
                ? (formatNumberWithCommas(values?.original_contract_value?.toLocaleString()) as string)
                : ''
            }
            arrow
          >
            <div>
              <NumberInputField
                isUpAndDowns={false}
                name="original_contract_value"
                labelText="Original Contract Value"
                // // placeholder="1,2,3..."
                className={`${styles.textField} ${!isEditMode ? '' : styles.comboHighlight} ${
                  formik.touched?.original_contract_value && Boolean(formik.errors.original_contract_value)
                    ? styles.errorBox
                    : ''
                }`}
                value={
                  values?.original_contract_value
                    ? (formatNumberWithCommas(values?.original_contract_value?.toLocaleString()) as string)
                    : ''
                }
                format="comma-separated"
                onChange={(value) => {
                  formik.setFieldValue('original_contract_value', value)
                }}
                onBlur={formik.handleBlur}
                sx={NUMBER_INPUT_STYLE}
                endAdornment={
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <AedIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                }
                maxLength={12}
                disabled={!isEditMode}
                error={formik.touched?.original_contract_value && Boolean(formik.errors.original_contract_value)}
                helperText={formik.touched?.original_contract_value && formik.errors.original_contract_value}
              />
            </div>
          </Tooltip>
          {/* Committed Cost */}
          <Tooltip
            title={
              values?.approved_vo_value
                ? (formatNumberWithCommas(values?.approved_vo_value?.toLocaleString()) as string)
                : ''
            }
            arrow
          >
            <div>
              <NumberInputField
                isUpAndDowns={false}
                minusValue={true}
                name="approved_vo_value"
                labelText="Approved VO Value"
                // placeholder="1,2,3..."
                className={`${styles.textField} ${!isEditMode ? '' : styles.comboHighlight} ${
                  formik.touched?.approved_vo_value && Boolean(formik.errors.approved_vo_value) ? styles.errorBox : ''
                }`}
                value={
                  values?.approved_vo_value
                    ? (formatNumberWithCommas(values?.approved_vo_value?.toLocaleString()) as string)
                    : ''
                }
                format="comma-separated"
                onChange={(value) => {
                  formik.setFieldValue('approved_vo_value', parseFloat(String(value)) || 0)
                }}
                onBlur={formik.handleBlur}
                sx={NUMBER_INPUT_STYLE}
                endAdornment={
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <AedIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                }
                maxLength={12}
                disabled={!isEditMode}
                error={formik.touched?.approved_vo_value && Boolean(formik.errors.approved_vo_value)}
                helperText={formik.touched?.approved_vo_value && formik.errors.approved_vo_value}
              />
            </div>
          </Tooltip>
          {/* revised_contract_value */}
          <Tooltip
            title={
              <div>
                <span>Original Contract Value + Approved VO Value </span>
                {/* <span>
                  {values?.revised_contract_value_aed
                    ? (formatNumberWithCommas(values?.revised_contract_value_aed?.toLocaleString()) as string)
                    : ''}
                </span> */}
              </div>
            }
            arrow
          >
            <div>
              <NumberInputField
                name="revised_contract_value"
                isUpAndDowns={false}
                labelText="Revised Contract Value"
                // placeholder="1,2,3..."
                className={`${styles.textField} ${
                  formik.touched?.revised_contract_value && Boolean(formik.errors.revised_contract_value)
                    ? styles.errorBox
                    : ''
                }`}
                // className={`${styles.textField} ${!isEditMode ? '' : styles.comboHighlight} ${
                //   formik.touched.revised_contract_value && Boolean(formik.errors.revised_contract_value)
                //     ? styles.errorBox
                //     : ''
                // }`}
                value={
                  values?.revised_contract_value
                    ? (formatNumberWithCommas(values?.revised_contract_value?.toLocaleString()) as string)
                    : ''
                }
                format="comma-separated"
                onChange={(value) => {
                  formik.setFieldValue('revised_contract_value', value)
                }}
                onBlur={formik.handleBlur}
                sx={NUMBER_INPUT_STYLE}
                endAdornment={
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <AedIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                }
                maxLength={12}
                // disabled={!isEditMode}
                disabled={true}
                error={formik.touched?.revised_contract_value && Boolean(formik.errors.revised_contract_value)}
                helperText={formik.touched?.revised_contract_value && formik.errors.revised_contract_value}
              />
            </div>
          </Tooltip>
          {/* Paid Amount */}
          <Tooltip
            title={values?.pvr_value ? (formatNumberWithCommas(values?.pvr_value?.toLocaleString()) as string) : ''}
            arrow
          >
            <div>
              <NumberInputField
                isUpAndDowns={false}
                minusValue={true}
                name="pvr_value"
                labelText="PVR"
                // placeholder="1,2,3..."
                className={`${styles.textField} ${!isEditMode ? '' : styles.comboHighlight} ${
                  formik.touched?.pvr_value && Boolean(formik.errors.pvr_value) ? styles.errorBox : ''
                }`}
                value={values?.pvr_value ? (formatNumberWithCommas(values?.pvr_value?.toLocaleString()) as string) : ''}
                format="comma-separated"
                onChange={(value) => {
                  formik.setFieldValue('pvr_value', parseFloat(String(value)) || 0)
                }}
                onBlur={formik.handleBlur}
                sx={NUMBER_INPUT_STYLE}
                endAdornment={
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <AedIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                }
                maxLength={12}
                disabled={!isEditMode}
                error={formik.touched?.pvr_value && Boolean(formik.errors.pvr_value)}
                helperText={formik.touched?.pvr_value && formik.errors.pvr_value}
              />
            </div>
          </Tooltip>
          {/* Forecast At Completion */}
          <Tooltip
            title={
              <div>
                <span>Revised Contract Value + PVR</span>
                {/* <span>
                  {values?.forecasted_contract_value_aed
                    ? (formatNumberWithCommas(values?.forecasted_contract_value_aed?.toLocaleString()) as string)
                    : ''}
                </span> */}
              </div>
            }
            arrow
          >
            <div>
              <NumberInputField
                isUpAndDowns={false}
                name="forecasted_contract_value"
                labelText="Forecasted Contract Value"
                // placeholder="1,2,3..."
                className={`${styles.textField} ${
                  formik.touched?.forecasted_contract_value && Boolean(formik.errors.forecasted_contract_value)
                    ? styles.errorBox
                    : ''
                }`}
                // className={`${styles.textField} ${!isEditMode ? '' : styles.comboHighlight} ${
                //   formik.touched.forecasted_contract_value && Boolean(formik.errors.forecasted_contract_value)
                //     ? styles.errorBox
                //     : ''
                // }`}
                value={
                  values?.forecasted_contract_value
                    ? (formatNumberWithCommas(values?.forecasted_contract_value?.toLocaleString()) as string)
                    : ''
                }
                format="comma-separated"
                onChange={(value) => {
                  formik.setFieldValue('forecasted_contract_value', value)
                }}
                onBlur={formik.handleBlur}
                sx={NUMBER_INPUT_STYLE}
                endAdornment={
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <AedIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                }
                maxLength={12}
                // disabled={!isEditMode}
                disabled={true}
                error={formik.touched?.forecasted_contract_value && Boolean(formik.errors.forecasted_contract_value)}
                helperText={formik.touched?.forecasted_contract_value && formik.errors.forecasted_contract_value}
              />
            </div>
          </Tooltip>

          <div>
            <NumberInputField
              isUpAndDowns={false}
              minusValue={true}
              name="paid_amount"
              labelText="Paid Amount"
              // placeholder="1,2,3..."
              className={`${styles.textField} ${!isEditMode ? '' : styles.comboHighlight}`}
              value={
                values?.paid_amount ? (formatNumberWithCommas(values?.paid_amount?.toLocaleString()) as string) : ''
              }
              format="comma-separated"
              onChange={(value) => {
                formik.setFieldValue('paid_amount', parseFloat(String(value)) || 0)
              }}
              onBlur={formik.handleBlur}
              sx={NUMBER_INPUT_STYLE}
              endAdornment={
                <InputAdornment position="start" className={styles.endAdornment}>
                  <AedIcon className={styles.endAdornmentIcon} />
                </InputAdornment>
              }
              disabled={!isEditMode}
              maxLength={12}
            />
          </div>

          {/* Contract Signature */}
          {/* <Tooltip
            title={
              values?.contract_signature
                ? (formatNumberWithCommas(values?.contract_signature?.toLocaleString()) as string)
                : ''
            }
            arrow
          >
            <div>
              <NumberInputField
                isUpAndDowns={false}
                name="contract_signature"
                labelText="Contract Signature"
                // placeholder="1,2,3..."
                className={`$ ${styles.textField} ${!isEditMode ? '' : styles.comboHighlight}`}
                value={
                  values?.contract_signature
                    ? (formatNumberWithCommas(values?.contract_signature?.toLocaleString()) as string)
                    : ''
                }
                format="comma-separated"
                onChange={(value) => {
                  formik.setFieldValue('contract_signature', value)
                }}
                onBlur={formik.handleBlur}
                sx={NUMBER_INPUT_STYLE}
                endAdornment={
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <AedIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                }
                disabled={!isEditMode}
                error={formik.touched.contract_signature && Boolean(formik.errors.contract_signature)}
                helperText={formik.touched.contract_signature && formik.errors.contract_signature}
              />
            </div>
          </Tooltip> */}

          {/* Bonds */}
          {/* <Tooltip
            title={values?.bonds ? (formatNumberWithCommas(values?.bonds?.toLocaleString()) as string) : ''}
            arrow
          >
            <div>
              <NumberInputField
                isUpAndDowns={false}
                name="bonds"
                labelText="Bonds"
                // placeholder="1,2,3..."
                className={`$ ${styles.textField} ${!isEditMode ? '' : styles.comboHighlight}`}
                value={values?.bonds ? (formatNumberWithCommas(values?.bonds?.toLocaleString()) as string) : ''}
                format="comma-separated"
                onChange={(value) => {
                  formik.setFieldValue('bonds', value)
                }}
                onBlur={formik.handleBlur}
                sx={NUMBER_INPUT_STYLE}
                endAdornment={
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <AedIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                }
                disabled={!isEditMode}
                error={formik.touched.bonds && Boolean(formik.errors.bonds)}
                helperText={formik.touched.bonds && formik.errors.bonds}
              />
            </div>
          </Tooltip> */}

          {/* Insurance */}
          {/* <Tooltip
            title={values?.insurance ? (formatNumberWithCommas(values?.insurance?.toLocaleString()) as string) : ''}
            arrow
          >
            <div>
              <NumberInputField
                isUpAndDowns={false}
                name="insurance"
                labelText="Insurance"
                // placeholder="1,2,3..."
                className={`$ ${styles.textField} ${!isEditMode ? '' : styles.comboHighlight}`}
                value={values?.insurance ? (formatNumberWithCommas(values?.insurance?.toLocaleString()) as string) : ''}
                format="comma-separated"
                onChange={(value) => {
                  formik.setFieldValue('insurance', value)
                }}
                onBlur={formik.handleBlur}
                sx={NUMBER_INPUT_STYLE}
                endAdornment={
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <AedIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                }
                disabled={!isEditMode}
                error={formik.touched.insurance && Boolean(formik.errors.insurance)}
                helperText={formik.touched.insurance && formik.errors.insurance}
              />
            </div>
          </Tooltip> */}

          {/* Advanced Payment */}
          {/* <Tooltip
            title={
              values?.advanced_payment
                ? (formatNumberWithCommas(values?.advanced_payment?.toLocaleString()) as string)
                : ''
            }
            arrow
          >
            <div>
              <NumberInputField
                isUpAndDowns={false}
                name="advanced_payment"
                labelText="Advanced Payment"
                // placeholder="1,2,3..."
                className={`$ ${styles.textField} ${!isEditMode ? '' : styles.comboHighlight}`}
                value={
                  values?.advanced_payment
                    ? (formatNumberWithCommas(values?.advanced_payment?.toLocaleString()) as string)
                    : ''
                }
                format="comma-separated"
                onChange={(value) => {
                  formik.setFieldValue('advanced_payment', value)
                }}
                onBlur={formik.handleBlur}
                sx={NUMBER_INPUT_STYLE}
                endAdornment={
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <AedIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                }
                disabled={!isEditMode}
                error={formik.touched.advanced_payment && Boolean(formik.errors.advanced_payment)}
                helperText={formik.touched.advanced_payment && formik.errors.advanced_payment}
              />
            </div>
          </Tooltip> */}
        </div>
      </div>
    </div>
  )
}

export default ContractFields
