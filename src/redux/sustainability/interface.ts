import { StatusEnum } from '../types'

export interface ISustainabilityState {
  getSustainabilitiesStatus: StatusEnum
  addSustainabilityStatus: StatusEnum
  deleteSustainabilityStatus: StatusEnum
  updateSustainabilityStatus: StatusEnum
  sustainabilities: ISustainability[]
  sustainability: ISustainability
}
export interface ISustainability {
  id?: string
  period: string
  project_name: string
  waste_recycled_percentage: number
  reinvented_economy_percentage: number
  reinvented_economy_value: number
  pearl_rating_percentage: number
  recycled_material_percentage: number
  workers_welfare_compliance_percentage: number
  last_updated: string
  renewable_energy: number
  carbon_emissions: number
  emissions_per_m2: number
  number_of_grievances: number
}

export interface IGetSustainabilityResponse {
  data: ISustainability[]
  message: string
  success: true
}
