import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetSustainabilityResponse, ISustainability, ISustainabilityState } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: ISustainabilityState = {
  getSustainabilitiesStatus: StatusEnum.Idle,
  addSustainabilityStatus: StatusEnum.Idle,
  deleteSustainabilityStatus: StatusEnum.Idle,
  updateSustainabilityStatus: StatusEnum.Idle,
  sustainabilities: [],
  sustainability: {
    period: '',
    project_name: '',
    waste_recycled_percentage: 0,
    reinvented_economy_percentage: 0,
    reinvented_economy_value: 0,
    pearl_rating_percentage: 0,
    recycled_material_percentage: 0,
    workers_welfare_compliance_percentage: 0,
    last_updated: '',
    renewable_energy: 0,
    carbon_emissions: 0,
    emissions_per_m2: 0,
    number_of_grievances: 0,
  },
}

export const getSustainabilities = createAsyncThunk(
  '/get-project-sustainability',
  async (data: { period: string; project_name?: string }, thunkAPI) => {
    const period = data.period
    try {
      const response = await ApiGetNoAuth(
        `/project-sustainability?period=${period}${data?.project_name ? `&project_name=${encodeURIComponent(data?.project_name)}` : ''}`,
      )
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const addSustainability = createAsyncThunk(
  '/add-project-sustainability',
  async (data: ISustainability, thunkAPI) => {
    try {
      const response = await ApiPostNoAuth(`/project-sustainability`, {
        period: data.period,
        project_name: data.project_name,
        waste_recycled_percentage: data.waste_recycled_percentage,
        reinvented_economy_percentage: data.reinvented_economy_percentage,
        reinvented_economy_value: data.reinvented_economy_value,
        pearl_rating_percentage: data.pearl_rating_percentage,
        recycled_material_percentage: data.recycled_material_percentage,
        workers_welfare_compliance_percentage: data.workers_welfare_compliance_percentage,
        renewable_energy: data.renewable_energy,
        carbon_emissions: data.carbon_emissions,
        emissions_per_m2: data.emissions_per_m2,
        number_of_grievances: data.number_of_grievances,
        last_updated: data.last_updated,
      })
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const updateSustainability = createAsyncThunk(
  '/update-project-sustainability',
  async (data: { id: string; data: ISustainability }, thunkAPI) => {
    try {
      const response = await ApiPutNoAuth(`/project-sustainability/${data.id}`, data.data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteSustainability = createAsyncThunk(
  '/delete-project-sustainability',
  async (data: number, thunkAPI) => {
    try {
      const response = await ApiDeleteNoAuth(`/project-sustainability/${data}`)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const sustainabilitySlice = createSlice({
  name: 'sustainability',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    //getSustainabilities
    builder.addCase(getSustainabilities.pending, (state) => {
      state.getSustainabilitiesStatus = StatusEnum.Pending
    })
    builder.addCase(getSustainabilities.fulfilled, (state, action) => {
      state.getSustainabilitiesStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetSustainabilityResponse
      state.sustainabilities = actionPayload.data
    })
    builder.addCase(getSustainabilities.rejected, (state) => {
      state.getSustainabilitiesStatus = StatusEnum.Failed
    })
    //addSustainability
    builder.addCase(addSustainability.pending, (state) => {
      state.addSustainabilityStatus = StatusEnum.Pending
    })
    builder.addCase(addSustainability.fulfilled, (state, action) => {
      state.addSustainabilityStatus = StatusEnum.Success
    })
    builder.addCase(addSustainability.rejected, (state) => {
      state.addSustainabilityStatus = StatusEnum.Failed
    })
    //updateSustainability
    builder.addCase(updateSustainability.pending, (state) => {
      state.updateSustainabilityStatus = StatusEnum.Pending
    })
    builder.addCase(updateSustainability.fulfilled, (state, action) => {
      state.updateSustainabilityStatus = StatusEnum.Success
    })
    builder.addCase(updateSustainability.rejected, (state) => {
      state.updateSustainabilityStatus = StatusEnum.Failed
    })
    //deleteSustainability
    builder.addCase(deleteSustainability.pending, (state) => {
      state.deleteSustainabilityStatus = StatusEnum.Pending
    })
    builder.addCase(deleteSustainability.fulfilled, (state, action) => {
      state.deleteSustainabilityStatus = StatusEnum.Success
    })
    builder.addCase(deleteSustainability.rejected, (state) => {
      state.deleteSustainabilityStatus = StatusEnum.Failed
    })
  },
})

export const {} = sustainabilitySlice.actions

// Export the reducer
export default sustainabilitySlice.reducer
