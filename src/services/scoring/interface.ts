export interface IScoring {
  id?: string
  quarter: string
  executive_entity: string | null
  label: string
  schedule_score: number | null
  variation_score: number | null
  expenditure_score: number | null
  overall_score?: number | null
}

export interface IScoringPayload {
  entityScore: IScoring[]
}

export interface IUpdateScoring {
  // TODO
  entityScore: {
    id?: string
    quarter: string
    executive_entity: string | null
    label: string
    schedule_score: number | null
    variation_score: number | null
    expenditure_score: number | null
    overall_score?: number | null
  }[]
}

export interface IGetMasterScoringResponse {
  data: IScoring[]
  message: string
  success: true
}
